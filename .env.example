# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# Google Hotel Center Configuration
GHC_PARTNER_KEY=your_partner_key_from_google
GHC_MAX_RESPONSE_TIME=100000
GHC_LIVE_PRICING_MAX_RESPONSE_TIME=4000
GHC_DEFAULT_CURRENCY=USD
GHC_SITE_ID=your_site_id
GHC_MAX_ADVANCE_PURCHASE=330
GHC_MAX_LENGTH_OF_STAY=30

# Third-Party Supplier API Configuration
SUPPLIER_API_BASE_URL=https://api.your-supplier.com/v1
SUPPLIER_API_KEY=your_supplier_api_key
SUPPLIER_API_TIMEOUT=30000
SUPPLIER_API_RETRY_ATTEMPTS=3
SUPPLIER_API_MAX_REQUESTS=100
SUPPLIER_API_WINDOW_MS=60000

# Supplier API Endpoints
SUPPLIER_API_PRICES_ENDPOINT=/prices/search
SUPPLIER_API_METADATA_ENDPOINT=/hotels/metadata
SUPPLIER_API_HEALTH_ENDPOINT=/health

# Hotel Mapping Configuration
ENABLE_HOTEL_MAPPING=false
HOTEL_MAPPING_FILE_PATH=./data/hotel-mappings.json
DEFAULT_ROOM_ID=standard
DEFAULT_PACKAGE_ID=base

# Caching Configuration
CACHE_ENABLED=false
CACHE_TTL=300
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/ghc-integration.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_CONSOLE_ENABLED=true

# Security Configuration
ENABLE_IP_WHITELIST=false
ALLOWED_IPS=***********/20,************/19,***********/18
API_KEY=your_api_key
HTTPS_ONLY=false

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_ENDPOINT=/metrics
HEALTH_ENDPOINT=/health

# Business Logic Configuration
DEFAULT_MARKUP=0.0
MINIMUM_PRICE=1.0
MAXIMUM_PRICE=10000.0
DEFAULT_REFUND_AVAILABLE=true
DEFAULT_REFUND_DAYS=1
DEFAULT_REFUND_TIME=18:00:00
DEFAULT_BREAKFAST=false
DEFAULT_INTERNET=true
DEFAULT_PARKING=false
