const request = require('supertest');
const GoogleHotelCenterServer = require('../src/server');

describe('Google Hotel Center Integration', () => {
  let app;
  let server;

  beforeAll(async () => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.SUPPLIER_API_BASE_URL = 'https://mock-supplier-api.com';
    process.env.SUPPLIER_API_KEY = 'test-key';
    process.env.LOG_CONSOLE_ENABLED = 'false';

    const serverInstance = new GoogleHotelCenterServer();
    app = serverInstance.app;
  });

  afterAll(async () => {
    if (server) {
      await new Promise(resolve => server.close(resolve));
    }
  });

  describe('GET /', () => {
    test('should return service information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body.service).toBe('Google Hotel Center Integration');
      expect(response.body.status).toBe('running');
      expect(response.body.endpoints).toBeDefined();
    });
  });

  describe('GET /health', () => {
    test('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect('Content-Type', /json/);

      expect(response.body.timestamp).toBeDefined();
      // Note: This might return 503 if supplier API is not available in test
    });
  });

  describe('POST /query', () => {
    test('should handle valid query XML', async () => {
      const queryXml = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <Checkin>2023-12-25</Checkin>
  <Nights>2</Nights>
  <PropertyList>
    <Property>test-hotel-1</Property>
  </PropertyList>
</Query>`;

      const response = await request(app)
        .post('/query')
        .set('Content-Type', 'application/xml')
        .set('User-Agent', 'Google-HotelAdsPrices')
        .send(queryXml);

      expect(response.headers['content-type']).toMatch(/application\/xml/);
      expect(response.text).toContain('<Transaction');
      expect(response.text).toContain('id=');
      expect(response.text).toContain('timestamp=');
    });

    test('should reject request with wrong content type', async () => {
      const response = await request(app)
        .post('/query')
        .set('Content-Type', 'application/json')
        .send('{}')
        .expect(400);

      expect(response.body.error).toBe('Bad Request');
      expect(response.body.message).toContain('Content-Type must be application/xml');
    });

    test('should handle invalid XML gracefully', async () => {
      const invalidXml = '<InvalidXML>';

      const response = await request(app)
        .post('/query')
        .set('Content-Type', 'application/xml')
        .set('User-Agent', 'Google-HotelAdsPrices')
        .send(invalidXml)
        .expect(500);

      expect(response.headers['content-type']).toMatch(/application\/xml/);
      expect(response.text).toContain('<Transaction');
    });

    test('should handle live pricing query with deadline', async () => {
      const livePricingXml = `<?xml version="1.0" encoding="UTF-8"?>
<Query latencySensitive="true">
  <Checkin>2023-12-25</Checkin>
  <Nights>1</Nights>
  <DeadlineMs>1000</DeadlineMs>
  <PropertyList>
    <Property>test-hotel-1</Property>
  </PropertyList>
  <Context>
    <Occupancy>2</Occupancy>
    <OccupancyDetails>
      <NumAdults>2</NumAdults>
    </OccupancyDetails>
    <UserCountry>US</UserCountry>
    <UserDevice>mobile</UserDevice>
  </Context>
</Query>`;

      const response = await request(app)
        .post('/query')
        .set('Content-Type', 'application/xml')
        .set('User-Agent', 'Google-HotelAdsPrices')
        .send(livePricingXml);

      expect(response.headers['content-type']).toMatch(/application\/xml/);
      expect(response.text).toContain('<Transaction');
      
      // Check that response time is reasonable for live pricing
      const responseTime = parseInt(response.headers['x-response-time']);
      expect(responseTime).toBeLessThan(5000); // Should be under 5 seconds
    });
  });

  describe('GET /metrics', () => {
    test('should return metrics when enabled', async () => {
      // This test depends on monitoring being enabled
      const response = await request(app)
        .get('/metrics');

      if (response.status === 200) {
        expect(response.body.uptime).toBeDefined();
        expect(response.body.memory).toBeDefined();
        expect(response.body.timestamp).toBeDefined();
      }
    });
  });

  describe('Error Handling', () => {
    test('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body.error).toBe('Not Found');
    });

    test('should handle malformed requests gracefully', async () => {
      const response = await request(app)
        .post('/query')
        .set('Content-Type', 'application/xml')
        .send('') // Empty body
        .expect(500);

      expect(response.headers['content-type']).toMatch(/application\/xml/);
    });
  });
});
