const TransactionGenerator = require('../src/generators/transactionGenerator');

describe('TransactionGenerator', () => {
  let generator;

  beforeEach(() => {
    generator = new TransactionGenerator();
  });

  describe('generateTransaction', () => {
    test('should generate transaction XML for standard pricing query', () => {
      const queryData = {
        type: 'standard',
        properties: ['hotel1'],
        checkin: '2023-05-23',
        nights: 2
      };

      const priceData = {
        hotels: [{
          hotelId: 'hotel1',
          available: true,
          rates: [{
            roomId: 'room1',
            packageId: 'package1',
            baseRate: 100.00,
            currency: 'USD',
            taxes: 10.00,
            fees: 5.00,
            totalPrice: 115.00,
            refundable: true,
            refundableUntilDays: 1,
            breakfastIncluded: false,
            internetIncluded: true,
            parkingIncluded: false,
            occupancy: 2,
            ratePlanId: 'room1-package1'
          }]
        }]
      };

      const xml = generator.generateTransaction(queryData, priceData);

      expect(xml).toContain('<Transaction');
      expect(xml).toContain('id=');
      expect(xml).toContain('timestamp=');
      expect(xml).toContain('<Property>hotel1</Property>');
      expect(xml).toContain('<Checkin>2023-05-23</Checkin>');
      expect(xml).toContain('<Nights>2</Nights>');
      expect(xml).toContain('<Baserate currency="USD">100.00</Baserate>');
      expect(xml).toContain('<Tax currency="USD">10.00</Tax>');
      expect(xml).toContain('<OtherFees currency="USD">5.00</OtherFees>');
      expect(xml).toContain('<InternetIncluded>1</InternetIncluded>');
    });

    test('should generate unavailable result for hotels with no rates', () => {
      const queryData = {
        type: 'standard',
        properties: ['hotel1'],
        checkin: '2023-05-23',
        nights: 2
      };

      const priceData = {
        hotels: [{
          hotelId: 'hotel1',
          available: false,
          rates: []
        }]
      };

      const xml = generator.generateTransaction(queryData, priceData);

      expect(xml).toContain('<Unavailable>');
      expect(xml).toContain('<NoVacancy/>');
      expect(xml).toContain('<Tax currency="USD">0</Tax>');
    });

    test('should generate metadata transaction', () => {
      const queryData = {
        type: 'metadata',
        properties: ['hotel1']
      };

      const priceData = {
        hotels: [{
          hotelId: 'hotel1',
          rooms: [{
            roomId: 'room1',
            name: 'Standard Room',
            description: 'A comfortable standard room',
            capacity: 4,
            photoUrl: 'https://example.com/room1.jpg'
          }],
          packages: [{
            packageId: 'package1',
            name: 'Standard Package',
            description: 'Basic package with amenities',
            occupancy: 2,
            breakfastIncluded: true,
            internetIncluded: true,
            parkingIncluded: false,
            refundable: true,
            refundableUntilDays: 1
          }]
        }]
      };

      const xml = generator.generateTransaction(queryData, priceData);

      expect(xml).toContain('<PropertyDataSet>');
      expect(xml).toContain('<RoomData>');
      expect(xml).toContain('<PackageData>');
      expect(xml).toContain('<RoomID>room1</RoomID>');
      expect(xml).toContain('<PackageID>package1</PackageID>');
      expect(xml).toContain('text="Standard Room"');
    });

    test('should handle multiple hotels', () => {
      const queryData = {
        type: 'standard',
        properties: ['hotel1', 'hotel2'],
        checkin: '2023-05-23',
        nights: 1
      };

      const priceData = {
        hotels: [
          {
            hotelId: 'hotel1',
            available: true,
            rates: [{
              roomId: 'room1',
              packageId: 'package1',
              baseRate: 100.00,
              currency: 'USD',
              taxes: 10.00,
              fees: 5.00,
              occupancy: 2,
              ratePlanId: 'room1-package1'
            }]
          },
          {
            hotelId: 'hotel2',
            available: true,
            rates: [{
              roomId: 'room2',
              packageId: 'package2',
              baseRate: 150.00,
              currency: 'USD',
              taxes: 15.00,
              fees: 7.50,
              occupancy: 2,
              ratePlanId: 'room2-package2'
            }]
          }
        ]
      };

      const xml = generator.generateTransaction(queryData, priceData);

      expect(xml).toContain('<Property>hotel1</Property>');
      expect(xml).toContain('<Property>hotel2</Property>');
      expect(xml).toContain('<Baserate currency="USD">100.00</Baserate>');
      expect(xml).toContain('<Baserate currency="USD">150.00</Baserate>');
    });
  });

  describe('generateErrorResponse', () => {
    test('should generate error response', () => {
      const queryData = {
        properties: ['hotel1', 'hotel2'],
        checkin: '2023-05-23',
        nights: 2
      };

      const xml = generator.generateErrorResponse(queryData, 'Supplier API unavailable');

      expect(xml).toContain('<Transaction');
      expect(xml).toContain('<Unavailable>');
      expect(xml).toContain('<NoVacancy/>');
      expect(xml).toContain('<Property>hotel1</Property>');
      expect(xml).toContain('<Property>hotel2</Property>');
    });
  });

  describe('validateTransaction', () => {
    test('should validate correct transaction XML', () => {
      const validXml = `<?xml version="1.0" encoding="UTF-8"?>
<Transaction id="test123" timestamp="2023-05-23T10:00:00Z">
  <Result>
    <Property>hotel1</Property>
  </Result>
</Transaction>`;

      expect(() => generator.validateTransaction(validXml)).not.toThrow();
    });

    test('should throw error for invalid transaction XML', () => {
      const invalidXml = '<InvalidXML></InvalidXML>';

      expect(() => generator.validateTransaction(invalidXml)).toThrow('Invalid Transaction XML: Missing Transaction element');
    });

    test('should throw error for missing timestamp', () => {
      const invalidXml = '<Transaction id="test123"></Transaction>';

      expect(() => generator.validateTransaction(invalidXml)).toThrow('Invalid Transaction XML: Missing timestamp attribute');
    });

    test('should throw error for missing id', () => {
      const invalidXml = '<Transaction timestamp="2023-05-23T10:00:00Z"></Transaction>';

      expect(() => generator.validateTransaction(invalidXml)).toThrow('Invalid Transaction XML: Missing id attribute');
    });
  });
});
