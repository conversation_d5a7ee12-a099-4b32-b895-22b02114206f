// Test setup file
process.env.NODE_ENV = 'test';
process.env.SUPPLIER_API_BASE_URL = 'https://mock-supplier-api.com';
process.env.SUPPLIER_API_KEY = 'test-key';
process.env.LOG_CONSOLE_ENABLED = 'false';
process.env.LOG_FILE_ENABLED = 'false';

// Mock console methods to reduce test noise
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};
