const QueryParser = require('../src/parsers/queryParser');

describe('QueryParser', () => {
  let parser;

  beforeEach(() => {
    parser = new QueryParser();
  });

  describe('parseQuery', () => {
    test('should parse standard pricing query', async () => {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <Checkin>2023-05-23</Checkin>
  <Nights>3</Nights>
  <PropertyList>
    <Property>pid5</Property>
    <Property>pid8</Property>
  </PropertyList>
</Query>`;

      const result = await parser.parseQuery(xml);

      expect(result.type).toBe('standard');
      expect(result.checkin).toBe('2023-05-23');
      expect(result.nights).toBe(3);
      expect(result.properties).toEqual(['pid5', 'pid8']);
      expect(result.isLivePricing).toBe(false);
    });

    test('should parse live pricing query with context', async () => {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Query latencySensitive="true">
  <Checkin>2023-05-23</Checkin>
  <Nights>2</Nights>
  <DeadlineMs>500</DeadlineMs>
  <PropertyList>
    <Property>6781291</Property>
  </PropertyList>
  <Context>
    <Occupancy>3</Occupancy>
    <OccupancyDetails>
      <NumAdults>2</NumAdults>
      <Children>
        <Child age="5"/>
      </Children>
    </OccupancyDetails>
    <UserCountry>US</UserCountry>
    <UserDevice>mobile</UserDevice>
  </Context>
</Query>`;

      const result = await parser.parseQuery(xml);

      expect(result.type).toBe('livePricing');
      expect(result.isLivePricing).toBe(true);
      expect(result.deadlineMs).toBe(500);
      expect(result.context.occupancy).toBe(3);
      expect(result.context.occupancyDetails.numAdults).toBe(2);
      expect(result.context.occupancyDetails.children).toHaveLength(1);
      expect(result.context.occupancyDetails.children[0].age).toBe(5);
      expect(result.context.userCountry).toBe('US');
      expect(result.context.userDevice).toBe('mobile');
    });

    test('should parse metadata query', async () => {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <HotelInfoProperties>
    <Property>pid5</Property>
    <Property>pid8</Property>
  </HotelInfoProperties>
</Query>`;

      const result = await parser.parseQuery(xml);

      expect(result.type).toBe('metadata');
      expect(result.properties).toEqual(['pid5', 'pid8']);
    });

    test('should parse date range query', async () => {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <FirstDate>2023-05-23</FirstDate>
  <LastDate>2023-05-26</LastDate>
  <Nights>3</Nights>
  <PropertyList>
    <Property>pid5</Property>
  </PropertyList>
</Query>`;

      const result = await parser.parseQuery(xml);

      expect(result.type).toBe('dateRange');
      expect(result.firstDate).toBe('2023-05-23');
      expect(result.lastDate).toBe('2023-05-26');
      expect(result.nights).toBe(3);
    });

    test('should parse context query', async () => {
      const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <Checkin>2023-05-23</Checkin>
  <Nights>2</Nights>
  <PropertyContextList>
    <PropertyContext>
      <Property>8675309</Property>
      <Context><UserCountry>US</UserCountry></Context>
      <Context><UserCountry>GB</UserCountry></Context>
    </PropertyContext>
  </PropertyContextList>
</Query>`;

      const result = await parser.parseQuery(xml);

      expect(result.type).toBe('context');
      expect(result.propertyContexts).toHaveLength(1);
      expect(result.propertyContexts[0].properties).toEqual(['8675309']);
      expect(result.propertyContexts[0].contexts).toHaveLength(2);
    });

    test('should throw error for invalid XML', async () => {
      const invalidXml = '<InvalidXML>';
      
      await expect(parser.parseQuery(invalidXml)).rejects.toThrow();
    });

    test('should throw error for missing Query element', async () => {
      const xml = '<?xml version="1.0" encoding="UTF-8"?><Root></Root>';
      
      await expect(parser.parseQuery(xml)).rejects.toThrow('Invalid Query XML: Missing Query root element');
    });
  });

  describe('validateQuery', () => {
    test('should validate standard query successfully', () => {
      const query = {
        type: 'standard',
        properties: ['hotel1', 'hotel2'],
        checkin: '2023-05-23',
        nights: 3
      };

      expect(() => parser.validateQuery(query)).not.toThrow();
    });

    test('should throw error for missing properties', () => {
      const query = {
        type: 'standard',
        properties: [],
        checkin: '2023-05-23',
        nights: 3
      };

      expect(() => parser.validateQuery(query)).toThrow('No properties specified in query');
    });

    test('should throw error for missing checkin', () => {
      const query = {
        type: 'standard',
        properties: ['hotel1'],
        nights: 3
      };

      expect(() => parser.validateQuery(query)).toThrow('Check-in date is required');
    });

    test('should throw error for invalid nights', () => {
      const query = {
        type: 'standard',
        properties: ['hotel1'],
        checkin: '2023-05-23',
        nights: 0
      };

      expect(() => parser.validateQuery(query)).toThrow('Valid nights value is required');
    });
  });
});
