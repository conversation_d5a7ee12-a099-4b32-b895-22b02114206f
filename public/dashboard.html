<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Hotel Center - Analytics Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header h1 { color: #1a73e8; margin-bottom: 10px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .status-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-card h3 { margin-bottom: 15px; color: #333; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .metric-value { font-weight: bold; }
        .status-ready { color: #34a853; }
        .status-warning { color: #fbbc04; }
        .status-error { color: #ea4335; }
        .progress-bar { width: 100%; height: 8px; background: #e0e0e0; border-radius: 4px; margin: 10px 0; }
        .progress-fill { height: 100%; border-radius: 4px; transition: width 0.3s ease; }
        .progress-good { background: #34a853; }
        .progress-warning { background: #fbbc04; }
        .progress-error { background: #ea4335; }
        .recommendations { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .recommendation { padding: 10px; margin: 10px 0; border-left: 4px solid; border-radius: 4px; }
        .rec-high { border-color: #ea4335; background: #fef7f0; }
        .rec-medium { border-color: #fbbc04; background: #fefbf0; }
        .rec-low { border-color: #34a853; background: #f0f8f0; }
        .properties-table { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e0e0e0; }
        th { background: #f8f9fa; font-weight: 600; }
        .refresh-btn { background: #1a73e8; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #1557b0; }
        .loading { text-align: center; padding: 40px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏨 Google Hotel Center Analytics Dashboard</h1>
            <p>Monitor your price coverage and accuracy to meet Google's requirements</p>
            <button class="refresh-btn" onclick="loadDashboard()">🔄 Refresh Data</button>
        </div>

        <div id="loading" class="loading">Loading dashboard data...</div>
        <div id="dashboard" style="display: none;">
            
            <div class="status-grid">
                <div class="status-card">
                    <h3>📊 Google Requirements Status</h3>
                    <div class="metric">
                        <span>Overall Status:</span>
                        <span id="overall-status" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Properties >50% Coverage:</span>
                        <span id="properties-above-50" class="metric-value">-</span>
                    </div>
                    <div class="progress-bar">
                        <div id="coverage-progress" class="progress-fill"></div>
                    </div>
                    <div class="metric">
                        <span>Price Accuracy:</span>
                        <span id="price-accuracy" class="metric-value">-</span>
                    </div>
                    <div class="progress-bar">
                        <div id="accuracy-progress" class="progress-fill"></div>
                    </div>
                </div>

                <div class="status-card">
                    <h3>📈 Coverage Metrics</h3>
                    <div class="metric">
                        <span>Total Properties:</span>
                        <span id="total-properties" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Overall Coverage:</span>
                        <span id="overall-coverage" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Properties >70%:</span>
                        <span id="properties-above-70" class="metric-value">-</span>
                    </div>
                </div>

                <div class="status-card">
                    <h3>🎯 Accuracy Metrics</h3>
                    <div class="metric">
                        <span>Excellent Properties:</span>
                        <span id="excellent-properties" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Good Properties:</span>
                        <span id="good-properties" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Poor Properties:</span>
                        <span id="poor-properties" class="metric-value">-</span>
                    </div>
                </div>

                <div class="status-card">
                    <h3>🔄 Update Status</h3>
                    <div class="metric">
                        <span>Last Update:</span>
                        <span id="last-update" class="metric-value">-</span>
                    </div>
                    <div class="metric">
                        <span>Success Rate:</span>
                        <span id="update-success-rate" class="metric-value">-</span>
                    </div>
                </div>
            </div>

            <div class="recommendations">
                <h3>💡 Recommendations</h3>
                <div id="recommendations-list">
                    <p>Loading recommendations...</p>
                </div>
            </div>

            <div class="properties-table">
                <h3>🏆 Top Performing Properties (>50% Coverage)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Property ID</th>
                            <th>Coverage %</th>
                            <th>Total Queries</th>
                            <th>Successful Responses</th>
                            <th>Last Queried</th>
                        </tr>
                    </thead>
                    <tbody id="properties-table-body">
                        <tr><td colspan="5">Loading properties...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        async function loadDashboard() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';

            try {
                const response = await fetch('/analytics/dashboard');
                const result = await response.json();
                
                if (result.success) {
                    updateDashboard(result.data);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to load dashboard:', error);
                document.getElementById('loading').innerHTML = `
                    <p style="color: #ea4335;">❌ Failed to load dashboard data</p>
                    <p>Error: ${error.message}</p>
                    <button class="refresh-btn" onclick="loadDashboard()">Try Again</button>
                `;
            }
        }

        function updateDashboard(data) {
            // Google Requirements Status
            const status = data.googleRequirements.status;
            const statusElement = document.getElementById('overall-status');
            statusElement.textContent = status;
            statusElement.className = `metric-value ${status === 'READY' ? 'status-ready' : 'status-error'}`;

            // Properties above 50%
            const propertiesAbove50 = data.googleRequirements.propertiesAbove50Current;
            const propertiesRequired = data.googleRequirements.propertiesAbove50Required;
            document.getElementById('properties-above-50').textContent = `${propertiesAbove50}/${propertiesRequired}`;
            
            const coverageProgress = Math.min((propertiesAbove50 / propertiesRequired) * 100, 100);
            const coverageProgressElement = document.getElementById('coverage-progress');
            coverageProgressElement.style.width = `${coverageProgress}%`;
            coverageProgressElement.className = `progress-fill ${coverageProgress >= 100 ? 'progress-good' : coverageProgress >= 60 ? 'progress-warning' : 'progress-error'}`;

            // Price Accuracy
            const accuracy = data.googleRequirements.accuracyCurrent;
            document.getElementById('price-accuracy').textContent = `${accuracy}%`;
            
            const accuracyProgressElement = document.getElementById('accuracy-progress');
            accuracyProgressElement.style.width = `${accuracy}%`;
            accuracyProgressElement.className = `progress-fill ${accuracy >= 95 ? 'progress-good' : accuracy >= 85 ? 'progress-warning' : 'progress-error'}`;

            // Coverage Metrics
            document.getElementById('total-properties').textContent = data.summary.totalProperties;
            document.getElementById('overall-coverage').textContent = `${data.summary.overallCoverage}%`;
            document.getElementById('properties-above-70').textContent = data.coverage.propertiesAbove70.length;

            // Accuracy Metrics
            document.getElementById('excellent-properties').textContent = data.accuracy.excellentProperties;
            document.getElementById('good-properties').textContent = data.accuracy.goodProperties;
            document.getElementById('poor-properties').textContent = data.accuracy.poorProperties;

            // Update Status
            document.getElementById('last-update').textContent = data.summary.lastUpdateTime ? 
                new Date(data.summary.lastUpdateTime).toLocaleString() : 'Never';
            document.getElementById('update-success-rate').textContent = `${data.summary.updateSuccessRate}%`;

            // Recommendations
            const recommendationsHtml = data.recommendations.map(rec => `
                <div class="recommendation rec-${rec.priority.toLowerCase()}">
                    <strong>${rec.priority}:</strong> ${rec.action}
                    <br><small>${rec.details}</small>
                </div>
            `).join('');
            document.getElementById('recommendations-list').innerHTML = recommendationsHtml || '<p>No recommendations at this time.</p>';

            // Properties Table
            const propertiesHtml = data.coverage.propertiesAbove50.map(prop => `
                <tr>
                    <td>${prop.propertyId}</td>
                    <td>${prop.coverage}%</td>
                    <td>${prop.totalQueries}</td>
                    <td>${prop.successfulResponses}</td>
                    <td>${prop.lastQueried ? new Date(prop.lastQueried).toLocaleString() : 'Never'}</td>
                </tr>
            `).join('');
            document.getElementById('properties-table-body').innerHTML = propertiesHtml || 
                '<tr><td colspan="5">No properties with >50% coverage yet</td></tr>';

            document.getElementById('loading').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
        }

        // Auto-refresh every 5 minutes
        setInterval(loadDashboard, 5 * 60 * 1000);

        // Load dashboard on page load
        loadDashboard();
    </script>
</body>
</html>
