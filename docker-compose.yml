version: '3.8'

services:
  ghc-integration:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      # Add your environment variables here
      - SUPPLIER_API_BASE_URL=${SUPPLIER_API_BASE_URL}
      - SUPPLIER_API_KEY=${SUPPLIER_API_KEY}
      - GHC_PARTNER_KEY=${GHC_PARTNER_KEY}
      - GHC_SITE_ID=${GHC_SITE_ID}
      - LOG_LEVEL=info
      - LOG_FILE_ENABLED=true
      - MONITORING_ENABLED=true
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

# volumes:
#   redis_data:
