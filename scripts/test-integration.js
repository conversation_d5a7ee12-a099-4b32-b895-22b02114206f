#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * Test script for Google Hotel Center Integration
 * This script simulates Google's Query requests to test your integration
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

async function testEndpoint(endpoint, method = 'GET', data = null, headers = {}) {
  try {
    console.log(`\n🧪 Testing ${method} ${endpoint}`);
    
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'User-Agent': 'Google-HotelAdsPrices',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const startTime = Date.now();
    const response = await axios(config);
    const duration = Date.now() - startTime;

    console.log(`✅ Success: ${response.status} (${duration}ms)`);
    console.log(`📊 Response size: ${JSON.stringify(response.data).length} bytes`);
    
    if (response.headers['content-type']?.includes('xml')) {
      console.log(`📄 XML Response preview: ${response.data.substring(0, 200)}...`);
    } else {
      console.log(`📄 Response:`, response.data);
    }

    return { success: true, response, duration };
  } catch (error) {
    console.log(`❌ Failed: ${error.response?.status || 'Network Error'}`);
    console.log(`📄 Error:`, error.response?.data || error.message);
    return { success: false, error };
  }
}

async function loadSampleQuery(filename) {
  const filePath = path.join(__dirname, '..', 'examples', filename);
  return fs.readFileSync(filePath, 'utf8');
}

async function runTests() {
  console.log('🚀 Starting Google Hotel Center Integration Tests');
  console.log(`🎯 Target URL: ${BASE_URL}`);

  const results = [];

  // Test 1: Health Check
  const healthResult = await testEndpoint('/health');
  results.push({ test: 'Health Check', ...healthResult });

  // Test 2: Service Info
  const infoResult = await testEndpoint('/');
  results.push({ test: 'Service Info', ...infoResult });

  // Test 3: Standard Pricing Query
  try {
    const standardQuery = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <Checkin>2023-12-25</Checkin>
  <Nights>2</Nights>
  <PropertyList>
    <Property>test-hotel-1</Property>
    <Property>test-hotel-2</Property>
  </PropertyList>
</Query>`;

    const standardResult = await testEndpoint('/query', 'POST', standardQuery, {
      'Content-Type': 'application/xml'
    });
    results.push({ test: 'Standard Pricing Query', ...standardResult });
  } catch (error) {
    console.log('❌ Could not load standard query');
  }

  // Test 4: Live Pricing Query
  const livePricingQuery = `<?xml version="1.0" encoding="UTF-8"?>
<Query latencySensitive="true">
  <Checkin>2023-12-25</Checkin>
  <Nights>1</Nights>
  <DeadlineMs>1000</DeadlineMs>
  <PropertyList>
    <Property>test-hotel-1</Property>
  </PropertyList>
  <Context>
    <Occupancy>2</Occupancy>
    <OccupancyDetails>
      <NumAdults>2</NumAdults>
    </OccupancyDetails>
    <UserCountry>US</UserCountry>
    <UserDevice>mobile</UserDevice>
  </Context>
</Query>`;

  const livePricingResult = await testEndpoint('/query', 'POST', livePricingQuery, {
    'Content-Type': 'application/xml'
  });
  results.push({ test: 'Live Pricing Query', ...livePricingResult });

  // Test 5: Metadata Query
  const metadataQuery = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <HotelInfoProperties>
    <Property>test-hotel-1</Property>
  </HotelInfoProperties>
</Query>`;

  const metadataResult = await testEndpoint('/query', 'POST', metadataQuery, {
    'Content-Type': 'application/xml'
  });
  results.push({ test: 'Metadata Query', ...metadataResult });

  // Test 6: Invalid Content Type
  const invalidContentTypeResult = await testEndpoint('/query', 'POST', '{}', {
    'Content-Type': 'application/json'
  });
  results.push({ test: 'Invalid Content Type', ...invalidContentTypeResult });

  // Test 7: Invalid XML
  const invalidXmlResult = await testEndpoint('/query', 'POST', '<InvalidXML>', {
    'Content-Type': 'application/xml'
  });
  results.push({ test: 'Invalid XML', ...invalidXmlResult });

  // Test 8: Metrics (if enabled)
  const metricsResult = await testEndpoint('/metrics');
  results.push({ test: 'Metrics', ...metricsResult });

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(50));
  
  let passed = 0;
  let failed = 0;

  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const duration = result.duration ? `(${result.duration}ms)` : '';
    console.log(`${status} ${result.test} ${duration}`);
    
    if (result.success) passed++;
    else failed++;
  });

  console.log('=' .repeat(50));
  console.log(`📈 Total: ${results.length}, Passed: ${passed}, Failed: ${failed}`);

  if (failed === 0) {
    console.log('🎉 All tests passed! Your integration is ready.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }

  return { total: results.length, passed, failed, results };
}

// Performance test
async function performanceTest() {
  console.log('\n🏃 Running Performance Test...');
  
  const query = `<?xml version="1.0" encoding="UTF-8"?>
<Query>
  <Checkin>2023-12-25</Checkin>
  <Nights>1</Nights>
  <PropertyList>
    <Property>perf-test-hotel</Property>
  </PropertyList>
</Query>`;

  const iterations = 10;
  const durations = [];

  for (let i = 0; i < iterations; i++) {
    const result = await testEndpoint('/query', 'POST', query, {
      'Content-Type': 'application/xml'
    });
    
    if (result.success) {
      durations.push(result.duration);
    }
  }

  if (durations.length > 0) {
    const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);
    
    console.log(`📊 Performance Results (${iterations} requests):`);
    console.log(`   Average: ${avg.toFixed(2)}ms`);
    console.log(`   Min: ${min}ms`);
    console.log(`   Max: ${max}ms`);
    
    if (avg < 1000) {
      console.log('✅ Performance looks good for live pricing queries');
    } else {
      console.log('⚠️  Average response time is high for live pricing queries');
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--performance')) {
    await performanceTest();
  } else {
    await runTests();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTests, performanceTest };
