# Google Hotel Center Integration - Pull Mechanism

A Node.js service that implements Google Hotel Center's pull mechanism for hotel price integration with third-party supplier APIs.

## Overview

This service acts as a bridge between Google Hotel Center and your third-party hotel price supplier. It implements the **Pull Delivery Mode** where:

1. Google sends XML Query messages to your endpoint requesting hotel prices
2. Your service fetches prices from your third-party supplier API
3. Your service responds with XML Transaction messages containing the pricing data
4. Google caches the responses and serves them to users

## Architecture

```
Google Hotel Center → Your API Service → Third-Party Supplier API
                   ←                  ←
```

## Features

- ✅ **Pull Mechanism Implementation** - Handles Google's Query messages and responds with Transaction messages
- ✅ **Live Pricing Support** - Handles real-time pricing queries with deadline constraints
- ✅ **Context Queries** - Supports user context (country, device, occupancy) for personalized pricing
- ✅ **Metadata Queries** - Handles requests for hotel room and package information
- ✅ **Date Range Queries** - Supports queries for multiple check-in dates
- ✅ **Error Handling** - Comprehensive error handling and logging
- ✅ **Configurable** - Extensive configuration options via environment variables
- ✅ **Testing** - Unit and integration tests included
- ✅ **Monitoring** - Health checks and metrics endpoints

## Quick Start

### 1. Installation

```bash
npm install
```

### 2. Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Required: Your third-party supplier API details
SUPPLIER_API_BASE_URL=https://api.your-supplier.com/v1
SUPPLIER_API_KEY=your_supplier_api_key

# Optional: Google Hotel Center settings
GHC_PARTNER_KEY=your_partner_key_from_google
GHC_SITE_ID=your_site_id
```

### 3. Start the Service

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

The service will start on `http://localhost:3000` by default.

### 4. Configure Google Hotel Center

Provide Google with your endpoint URL during integration setup:
- **Query Endpoint**: `https://your-domain.com/query`
- **Content-Type**: `application/xml`
- **Method**: `POST`

## API Endpoints

### POST /query
Main endpoint for Google Hotel Center queries.

**Headers:**
- `Content-Type: application/xml`
- `User-Agent: Google-HotelAdsPrices`

**Request Body:** XML Query message from Google

**Response:** XML Transaction message with pricing data

### GET /health
Health check endpoint for monitoring.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-05-23T10:00:00Z",
  "services": {
    "supplier": {
      "status": "healthy",
      "responseTime": "150ms"
    }
  }
}
```

### GET /metrics
Basic metrics endpoint (when monitoring is enabled).

## Query Types Supported

### 1. Standard Pricing Query
```xml
<Query>
  <Checkin>2023-05-23</Checkin>
  <Nights>3</Nights>
  <PropertyList>
    <Property>hotel1</Property>
    <Property>hotel2</Property>
  </PropertyList>
</Query>
```

### 2. Live Pricing Query
```xml
<Query latencySensitive="true">
  <Checkin>2023-05-23</Checkin>
  <Nights>2</Nights>
  <DeadlineMs>500</DeadlineMs>
  <PropertyList>
    <Property>hotel1</Property>
  </PropertyList>
  <Context>
    <Occupancy>3</Occupancy>
    <UserCountry>US</UserCountry>
    <UserDevice>mobile</UserDevice>
  </Context>
</Query>
```

### 3. Metadata Query
```xml
<Query>
  <HotelInfoProperties>
    <Property>hotel1</Property>
    <Property>hotel2</Property>
  </HotelInfoProperties>
</Query>
```

## Third-Party Supplier API Integration

The service expects your supplier API to accept requests in this format:

```json
{
  "hotel_ids": ["hotel1", "hotel2"],
  "check_in": "2023-05-23",
  "check_out": "2023-05-26",
  "nights": 3,
  "occupancy": {
    "adults": 2,
    "children": []
  },
  "currency": "USD",
  "user_country": "US",
  "device_type": "desktop"
}
```

And respond with:

```json
{
  "hotels": [
    {
      "hotel_id": "hotel1",
      "available": true,
      "rates": [
        {
          "room_id": "room1",
          "package_id": "standard",
          "base_rate": 100.00,
          "currency": "USD",
          "taxes": 10.00,
          "fees": 5.00,
          "total_price": 115.00,
          "refundable": true,
          "breakfast_included": false,
          "internet_included": true,
          "parking_included": false
        }
      ]
    }
  ]
}
```

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm test -- --coverage
```

## Configuration Options

See `.env.example` for all available configuration options including:

- **Server settings** (port, host)
- **Google Hotel Center settings** (partner key, timeouts, currency)
- **Supplier API settings** (URL, authentication, retry logic)
- **Security settings** (IP whitelisting, HTTPS enforcement)
- **Logging settings** (level, file output, console output)
- **Business logic settings** (markup, refund policies, amenities)

## Deployment

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production

Ensure these are set in production:
- `SUPPLIER_API_BASE_URL`
- `SUPPLIER_API_KEY`
- `GHC_PARTNER_KEY`
- `NODE_ENV=production`
- `LOG_LEVEL=warn`

## Monitoring

The service provides several monitoring capabilities:

- **Health Checks**: `/health` endpoint
- **Metrics**: `/metrics` endpoint (basic)
- **Structured Logging**: JSON logs with correlation IDs
- **Error Tracking**: Comprehensive error logging and reporting

## Security

- **IP Whitelisting**: Optional IP restriction for Google's servers
- **Request Validation**: Validates XML content and headers
- **Rate Limiting**: Prevents abuse with configurable limits
- **Security Headers**: Uses Helmet.js for security headers

## Troubleshooting

### Common Issues

1. **Supplier API Timeout**: Increase `SUPPLIER_API_TIMEOUT`
2. **Google Query Timeout**: Check `GHC_MAX_RESPONSE_TIME` setting
3. **Invalid XML Response**: Check supplier API response format
4. **Missing Prices**: Verify hotel ID mappings and supplier API availability

### Logs

Check logs for detailed information:
```bash
tail -f logs/ghc-integration.log
```

## Support

For Google Hotel Center specific questions, contact your Technical Account Manager (TAM).

For integration issues, check the logs and ensure your supplier API is responding correctly.
