const xml2js = require('xml2js');
const logger = require('../utils/logger');

class TransactionGenerator {
  constructor() {
    this.builder = new xml2js.Builder({
      rootName: 'Transaction',
      xmldec: { version: '1.0', encoding: 'UTF-8' },
      renderOpts: { pretty: true, indent: '  ' }
    });
  }

  /**
   * Generate Transaction XML response for Google Hotel Center
   * @param {Object} queryData - Parsed query data
   * @param {Object} priceData - Price data from supplier API
   * @returns {string} XML Transaction message
   */
  generateTransaction(queryData, priceData) {
    try {
      const transactionId = this.generateTransactionId();
      const timestamp = new Date().toISOString();

      const transaction = {
        $: {
          id: transactionId,
          timestamp: timestamp
        }
      };

      // Add results based on query type
      if (queryData.type === 'metadata') {
        transaction.PropertyDataSet = this.generatePropertyDataSets(priceData.hotels);
      } else {
        transaction.Result = this.generateResults(queryData, priceData.hotels);
      }

      const xml = this.builder.buildObject(transaction);
      
      logger.info('Transaction XML generated', {
        transactionId,
        resultCount: Array.isArray(transaction.Result) ? transaction.Result.length : 1,
        queryType: queryData.type
      });

      return xml;
    } catch (error) {
      logger.error('Failed to generate Transaction XML', { error: error.message });
      throw new Error(`Transaction generation failed: ${error.message}`);
    }
  }

  /**
   * Generate Result elements for pricing queries
   */
  generateResults(queryData, hotels) {
    const results = [];

    hotels.forEach(hotel => {
      if (!hotel.available || !hotel.rates || hotel.rates.length === 0) {
        // Generate unavailable result
        results.push(this.generateUnavailableResult(hotel.hotelId, queryData));
        return;
      }

      hotel.rates.forEach(rate => {
        const result = {
          Property: hotel.hotelId,
          Checkin: queryData.checkin,
          Nights: queryData.nights,
          Baserate: {
            $: { currency: rate.currency },
            _: rate.baseRate.toFixed(2)
          },
          Tax: {
            $: { currency: rate.currency },
            _: rate.taxes.toFixed(2)
          },
          OtherFees: {
            $: { currency: rate.currency },
            _: rate.fees.toFixed(2)
          }
        };

        // Add optional elements
        if (rate.roomId && rate.roomId !== 'default') {
          result.RoomID = rate.roomId;
        }

        if (rate.packageId && rate.packageId !== 'standard') {
          result.PackageID = rate.packageId;
        }

        if (rate.ratePlanId) {
          result.RatePlanID = rate.ratePlanId;
        }

        // Add occupancy for room bundles
        if (rate.occupancy && rate.occupancy !== 2) {
          result.Occupancy = rate.occupancy;
        }

        // Add amenities
        if (rate.breakfastIncluded) {
          result.BreakfastIncluded = '1';
        }

        if (rate.internetIncluded) {
          result.InternetIncluded = '1';
        }

        if (rate.parkingIncluded) {
          result.ParkingIncluded = '1';
        }

        // Add refund policy
        if (rate.refundable) {
          result.Refundable = {
            $: {
              available: 'true',
              refundable_until_days: rate.refundableUntilDays.toString(),
              refundable_until_time: '18:00:00'
            }
          };
        }

        // Add charge currency
        result.ChargeCurrency = 'web'; // Default to web, can be configured

        // Add allowable points of sale (configure based on your setup)
        result.AllowablePointsOfSale = {
          PointOfSale: {
            $: { id: 'your_site_id' }
          }
        };

        results.push(result);
      });
    });

    return results.length === 1 ? results[0] : results;
  }

  /**
   * Generate unavailable result for hotels with no rates
   */
  generateUnavailableResult(hotelId, queryData) {
    return {
      Property: hotelId,
      Checkin: queryData.checkin,
      Nights: queryData.nights,
      Unavailable: {
        NoVacancy: ''
      },
      Tax: {
        $: { currency: 'USD' },
        _: '0'
      },
      OtherFees: {
        $: { currency: 'USD' },
        _: '0'
      }
    };
  }

  /**
   * Generate PropertyDataSet for metadata queries
   */
  generatePropertyDataSets(hotels) {
    const propertyDataSets = [];

    hotels.forEach(hotel => {
      const propertyDataSet = {
        Property: hotel.hotelId
      };

      // Add room data if available
      if (hotel.rooms && hotel.rooms.length > 0) {
        propertyDataSet.RoomData = hotel.rooms.map(room => ({
          RoomID: room.roomId,
          Name: {
            Text: {
              $: { text: room.name, language: 'en' }
            }
          },
          Description: room.description ? {
            Text: {
              $: { text: room.description, language: 'en' }
            }
          } : undefined,
          Capacity: room.capacity || 4,
          PhotoURL: room.photoUrl ? {
            URL: room.photoUrl,
            Caption: {
              Text: {
                $: { text: room.photoCaption || 'Room Photo', language: 'en' }
              }
            }
          } : undefined
        }));
      }

      // Add package data if available
      if (hotel.packages && hotel.packages.length > 0) {
        propertyDataSet.PackageData = hotel.packages.map(pkg => ({
          PackageID: pkg.packageId,
          Name: {
            Text: {
              $: { text: pkg.name, language: 'en' }
            }
          },
          Description: pkg.description ? {
            Text: {
              $: { text: pkg.description, language: 'en' }
            }
          } : undefined,
          Occupancy: pkg.occupancy || 2,
          ChargeCurrency: pkg.chargeCurrency || 'web',
          BreakfastIncluded: pkg.breakfastIncluded ? '1' : '0',
          InternetIncluded: pkg.internetIncluded ? '1' : '0',
          ParkingIncluded: pkg.parkingIncluded ? '1' : '0',
          Refundable: pkg.refundable ? {
            $: {
              available: 'true',
              refundable_until_days: pkg.refundableUntilDays?.toString() || '1',
              refundable_until_time: '18:00:00'
            }
          } : undefined
        }));
      }

      propertyDataSets.push(propertyDataSet);
    });

    return propertyDataSets.length === 1 ? propertyDataSets[0] : propertyDataSets;
  }

  /**
   * Generate unique transaction ID
   */
  generateTransactionId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `txn_${timestamp}_${random}`;
  }

  /**
   * Generate error response for failed queries
   */
  generateErrorResponse(queryData, errorMessage) {
    const transactionId = this.generateTransactionId();
    const timestamp = new Date().toISOString();

    const transaction = {
      $: {
        id: transactionId,
        timestamp: timestamp
      },
      Result: queryData.properties?.map(propertyId => ({
        Property: propertyId,
        Checkin: queryData.checkin,
        Nights: queryData.nights,
        Unavailable: {
          NoVacancy: ''
        },
        Tax: {
          $: { currency: 'USD' },
          _: '0'
        },
        OtherFees: {
          $: { currency: 'USD' },
          _: '0'
        }
      })) || []
    };

    const xml = this.builder.buildObject(transaction);
    
    logger.warn('Error response generated', {
      transactionId,
      error: errorMessage,
      propertyCount: queryData.properties?.length || 0
    });

    return xml;
  }

  /**
   * Validate generated XML against basic requirements
   */
  validateTransaction(xml) {
    // Basic validation checks
    if (!xml.includes('<Transaction')) {
      throw new Error('Invalid Transaction XML: Missing Transaction element');
    }

    if (!xml.includes('timestamp=')) {
      throw new Error('Invalid Transaction XML: Missing timestamp attribute');
    }

    if (!xml.includes('id=')) {
      throw new Error('Invalid Transaction XML: Missing id attribute');
    }

    return true;
  }
}

module.exports = TransactionGenerator;
