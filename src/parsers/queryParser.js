const xml2js = require('xml2js');
const logger = require('../utils/logger');

class QueryParser {
  constructor() {
    this.parser = new xml2js.Parser({
      explicitArray: false,
      ignoreAttrs: false,
      mergeAttrs: true
    });
  }

  /**
   * Parse Google Hotel Center Query XML message
   * @param {string} xmlString - Raw XML string from Google
   * @returns {Object} Parsed query object
   */
  async parseQuery(xmlString) {
    try {
      const result = await this.parser.parseStringPromise(xmlString);
      const query = result.Query;

      if (!query) {
        throw new Error('Invalid Query XML: Missing Query root element');
      }

      const parsedQuery = {
        type: this.determineQueryType(query),
        timestamp: new Date().toISOString(),
        raw: xmlString
      };

      // Parse based on query type
      if (query.PropertyList) {
        // Standard pricing query
        parsedQuery.properties = this.parsePropertyList(query.PropertyList);
        parsedQuery.checkin = query.Checkin;
        parsedQuery.nights = parseInt(query.Nights);
        parsedQuery.isLivePricing = query.latencySensitive === 'true';
        parsedQuery.deadlineMs = query.DeadlineMs ? parseInt(query.DeadlineMs) : null;
        parsedQuery.context = this.parseContext(query.Context);
      } else if (query.PropertyContextList) {
        // Query with context
        parsedQuery.propertyContexts = this.parsePropertyContextList(query.PropertyContextList);
        parsedQuery.checkin = query.Checkin;
        parsedQuery.nights = parseInt(query.Nights);
      } else if (query.HotelInfoProperties) {
        // Metadata query
        parsedQuery.properties = this.parsePropertyList(query.HotelInfoProperties);
      } else if (query.FirstDate && query.LastDate) {
        // Date range query
        parsedQuery.firstDate = query.FirstDate;
        parsedQuery.lastDate = query.LastDate;
        parsedQuery.nights = query.Nights ? parseInt(query.Nights) : null;
        parsedQuery.affectedNights = query.AffectedNights ? parseInt(query.AffectedNights) : null;
        parsedQuery.properties = this.parsePropertyList(query.PropertyList);
      }

      logger.info('Query parsed successfully', {
        type: parsedQuery.type,
        propertyCount: parsedQuery.properties?.length || 0,
        isLivePricing: parsedQuery.isLivePricing
      });

      return parsedQuery;
    } catch (error) {
      logger.error('Failed to parse Query XML', { error: error.message, xml: xmlString });
      throw new Error(`Query parsing failed: ${error.message}`);
    }
  }

  /**
   * Determine the type of query based on its structure
   */
  determineQueryType(query) {
    if (query.HotelInfoProperties) return 'metadata';
    if (query.PropertyContextList) return 'context';
    if (query.FirstDate && query.LastDate) return 'dateRange';
    if (query.latencySensitive === 'true') return 'livePricing';
    return 'standard';
  }

  /**
   * Parse property list from various query formats
   */
  parsePropertyList(propertyList) {
    if (!propertyList) return [];

    if (Array.isArray(propertyList.Property)) {
      return propertyList.Property;
    } else if (propertyList.Property) {
      return [propertyList.Property];
    }

    return [];
  }

  /**
   * Parse context information for live pricing queries
   */
  parseContext(context) {
    if (!context) return null;

    const parsedContext = {};

    if (context.Occupancy) {
      parsedContext.occupancy = parseInt(context.Occupancy);
    }

    if (context.OccupancyDetails) {
      parsedContext.occupancyDetails = {
        numAdults: parseInt(context.OccupancyDetails.NumAdults) || 2,
        children: []
      };

      if (context.OccupancyDetails.Children && context.OccupancyDetails.Children.Child) {
        const children = Array.isArray(context.OccupancyDetails.Children.Child)
          ? context.OccupancyDetails.Children.Child
          : [context.OccupancyDetails.Children.Child];

        parsedContext.occupancyDetails.children = children.map(child => ({
          age: parseInt(child.age)
        }));
      }
    }

    if (context.UserCountry) {
      parsedContext.userCountry = context.UserCountry;
    }

    if (context.UserDevice) {
      parsedContext.userDevice = context.UserDevice;
    }

    return parsedContext;
  }

  /**
   * Parse property context list for context queries
   */
  parsePropertyContextList(propertyContextList) {
    if (!propertyContextList || !propertyContextList.PropertyContext) {
      return [];
    }

    const contexts = Array.isArray(propertyContextList.PropertyContext)
      ? propertyContextList.PropertyContext
      : [propertyContextList.PropertyContext];

    return contexts.map(ctx => ({
      properties: this.parsePropertyList({ Property: ctx.Property }),
      contexts: Array.isArray(ctx.Context) ? ctx.Context : [ctx.Context],
      occupancy: ctx.Occupancy ? parseInt(ctx.Occupancy) : null,
      occupancyDetails: ctx.OccupancyDetails ? this.parseOccupancyDetails(ctx.OccupancyDetails) : null
    }));
  }

  /**
   * Parse occupancy details
   */
  parseOccupancyDetails(occupancyDetails) {
    const details = {
      numAdults: parseInt(occupancyDetails.NumAdults) || 2,
      children: []
    };

    if (occupancyDetails.Children && occupancyDetails.Children.Child) {
      const children = Array.isArray(occupancyDetails.Children.Child)
        ? occupancyDetails.Children.Child
        : [occupancyDetails.Children.Child];

      details.children = children.map(child => ({
        age: parseInt(child.age)
      }));
    }

    return details;
  }

  /**
   * Validate parsed query for required fields
   */
  validateQuery(parsedQuery) {
    const errors = [];

    if (!parsedQuery.type) {
      errors.push('Query type could not be determined');
    }

    if (parsedQuery.type === 'standard' || parsedQuery.type === 'livePricing') {
      if (!parsedQuery.properties || parsedQuery.properties.length === 0) {
        errors.push('No properties specified in query');
      }
      if (!parsedQuery.checkin) {
        errors.push('Check-in date is required');
      }
      if (!parsedQuery.nights || parsedQuery.nights <= 0) {
        errors.push('Valid nights value is required');
      }
    }

    if (errors.length > 0) {
      throw new Error(`Query validation failed: ${errors.join(', ')}`);
    }

    return true;
  }
}

module.exports = QueryParser;
