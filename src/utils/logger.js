const winston = require('winston');
const config = require('../config/config');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: { service: 'google-hotel-center' },
  transports: []
});

// Add console transport if enabled
if (config.logging.console.enabled) {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// Add file transport if enabled
if (config.logging.file.enabled) {
  logger.add(new winston.transports.File({
    filename: config.logging.file.filename,
    maxsize: config.logging.file.maxSize,
    maxFiles: config.logging.file.maxFiles
  }));
}

module.exports = logger;
