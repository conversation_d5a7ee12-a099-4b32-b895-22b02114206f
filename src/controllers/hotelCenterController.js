const QueryParser = require('../parsers/queryParser');
const SupplierApiClient = require('../services/supplierApiClient');
const TransactionGenerator = require('../generators/transactionGenerator');
const logger = require('../utils/logger');

class HotelCenterController {
  constructor(config) {
    this.queryParser = new QueryParser();
    this.supplierClient = new SupplierApiClient(config);
    this.transactionGenerator = new TransactionGenerator();
    this.config = config;
  }

  /**
   * Handle Google Hotel Center Query requests
   * Main endpoint for pull mechanism
   */
  async handleQuery(req, res) {
    const startTime = Date.now();
    let parsedQuery = null;

    try {
      // Validate request headers
      this.validateHeaders(req);

      // Get raw XML from request body
      const xmlBody = this.extractXmlBody(req);
      logger.info('Received Google Hotel Center query', {
        contentLength: xmlBody.length,
        userAgent: req.headers['user-agent']
      });

      // Parse the Query XML
      parsedQuery = await this.queryParser.parseQuery(xmlBody);
      this.queryParser.validateQuery(parsedQuery);

      // Handle different query types
      let responseXml;
      switch (parsedQuery.type) {
        case 'standard':
        case 'livePricing':
          responseXml = await this.handlePricingQuery(parsedQuery);
          break;
        case 'context':
          responseXml = await this.handleContextQuery(parsedQuery);
          break;
        case 'metadata':
          responseXml = await this.handleMetadataQuery(parsedQuery);
          break;
        case 'dateRange':
          responseXml = await this.handleDateRangeQuery(parsedQuery);
          break;
        default:
          throw new Error(`Unsupported query type: ${parsedQuery.type}`);
      }

      // Send response
      const duration = Date.now() - startTime;
      this.sendXmlResponse(res, responseXml, duration);

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Query handling failed', {
        error: error.message,
        duration: `${duration}ms`,
        queryType: parsedQuery?.type
      });

      // Send error response
      const errorXml = parsedQuery 
        ? this.transactionGenerator.generateErrorResponse(parsedQuery, error.message)
        : this.generateGenericErrorResponse(error.message);
      
      this.sendXmlResponse(res, errorXml, duration, 500);
    }
  }

  /**
   * Handle standard pricing queries
   */
  async handlePricingQuery(parsedQuery) {
    const priceRequest = {
      hotelIds: parsedQuery.properties,
      checkin: parsedQuery.checkin,
      nights: parsedQuery.nights,
      occupancy: parsedQuery.context?.occupancyDetails || { adults: 2, children: [] },
      userCountry: parsedQuery.context?.userCountry,
      userDevice: parsedQuery.context?.userDevice,
      currency: 'USD' // Can be configured
    };

    // Apply timeout for live pricing queries
    if (parsedQuery.isLivePricing && parsedQuery.deadlineMs) {
      const timeoutMs = Math.min(parsedQuery.deadlineMs - 100, 4000); // Leave 100ms buffer
      priceRequest.timeout = timeoutMs;
    }

    const priceData = await this.supplierClient.fetchHotelPrices(priceRequest);
    return this.transactionGenerator.generateTransaction(parsedQuery, priceData);
  }

  /**
   * Handle context queries (with user context)
   */
  async handleContextQuery(parsedQuery) {
    const results = [];

    for (const propertyContext of parsedQuery.propertyContexts) {
      for (const context of propertyContext.contexts) {
        const priceRequest = {
          hotelIds: propertyContext.properties,
          checkin: parsedQuery.checkin,
          nights: parsedQuery.nights,
          occupancy: propertyContext.occupancyDetails || { adults: 2, children: [] },
          userCountry: context.UserCountry,
          userDevice: context.UserDevice,
          currency: 'USD'
        };

        const priceData = await this.supplierClient.fetchHotelPrices(priceRequest);
        const contextQuery = { ...parsedQuery, properties: propertyContext.properties };
        const transactionXml = this.transactionGenerator.generateTransaction(contextQuery, priceData);
        results.push(transactionXml);
      }
    }

    // Combine multiple transaction results if needed
    return results.length === 1 ? results[0] : this.combineTransactions(results);
  }

  /**
   * Handle metadata queries
   */
  async handleMetadataQuery(parsedQuery) {
    // For metadata queries, we might need to fetch room/package info
    // This depends on how your supplier API provides metadata
    const metadataRequest = {
      hotelIds: parsedQuery.properties,
      includeRooms: true,
      includePackages: true
    };

    // You might have a separate metadata endpoint or include it in price response
    const metadataData = await this.supplierClient.fetchHotelMetadata?.(metadataRequest) || 
                          await this.supplierClient.fetchHotelPrices(metadataRequest);
    
    return this.transactionGenerator.generateTransaction(parsedQuery, metadataData);
  }

  /**
   * Handle date range queries
   */
  async handleDateRangeQuery(parsedQuery) {
    const results = [];
    const startDate = new Date(parsedQuery.firstDate);
    const endDate = new Date(parsedQuery.lastDate);

    // Generate all possible check-in dates within the range
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const checkinDate = date.toISOString().split('T')[0];
      
      const priceRequest = {
        hotelIds: parsedQuery.properties,
        checkin: checkinDate,
        nights: parsedQuery.nights || parsedQuery.affectedNights,
        occupancy: { adults: 2, children: [] },
        currency: 'USD'
      };

      const priceData = await this.supplierClient.fetchHotelPrices(priceRequest);
      const dateQuery = { ...parsedQuery, checkin: checkinDate, nights: priceRequest.nights };
      const transactionXml = this.transactionGenerator.generateTransaction(dateQuery, priceData);
      results.push(transactionXml);
    }

    return this.combineTransactions(results);
  }

  /**
   * Validate request headers
   */
  validateHeaders(req) {
    const contentType = req.headers['content-type'];
    if (!contentType || !contentType.includes('application/xml')) {
      throw new Error('Invalid Content-Type header. Expected application/xml');
    }

    const userAgent = req.headers['user-agent'];
    if (!userAgent || (!userAgent.includes('Google-HotelAdsPrices') && !userAgent.includes('Google-TravelAds-Live'))) {
      logger.warn('Unexpected User-Agent header', { userAgent });
    }
  }

  /**
   * Extract XML body from request
   */
  extractXmlBody(req) {
    if (typeof req.body === 'string') {
      return req.body;
    } else if (req.body && typeof req.body === 'object') {
      return JSON.stringify(req.body);
    } else {
      throw new Error('Invalid request body format');
    }
  }

  /**
   * Send XML response to Google
   */
  sendXmlResponse(res, xml, duration, statusCode = 200) {
    res.set({
      'Content-Type': 'application/xml; charset=utf-8',
      'Cache-Control': 'no-cache',
      'X-Response-Time': `${duration}ms`
    });

    res.status(statusCode).send(xml);

    logger.info('Response sent to Google', {
      statusCode,
      duration: `${duration}ms`,
      responseSize: xml.length
    });
  }

  /**
   * Generate generic error response when query parsing fails
   */
  generateGenericErrorResponse(errorMessage) {
    const transactionId = `error_${Date.now()}`;
    const timestamp = new Date().toISOString();

    return `<?xml version="1.0" encoding="UTF-8"?>
<Transaction id="${transactionId}" timestamp="${timestamp}">
  <!-- Error: ${errorMessage} -->
</Transaction>`;
  }

  /**
   * Combine multiple transaction XMLs (for complex queries)
   */
  combineTransactions(transactions) {
    // For simplicity, return the first transaction
    // In a production system, you might want to merge results
    return transactions[0] || this.generateGenericErrorResponse('No results available');
  }

  /**
   * Health check endpoint
   */
  async healthCheck(req, res) {
    try {
      const supplierHealth = await this.supplierClient.healthCheck();
      
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          supplier: supplierHealth
        }
      };

      res.json(health);
    } catch (error) {
      logger.error('Health check failed', { error: error.message });
      res.status(503).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = HotelCenterController;
