const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

/**
 * Analytics routes for Google Hotel Center integration monitoring
 */

/**
 * GET /analytics/coverage
 * Get detailed price coverage analytics
 */
router.get('/coverage', async (req, res) => {
  try {
    const { coverageTracker } = req.app.locals;
    const report = coverageTracker.generateCoverageReport();
    
    res.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Coverage analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to generate coverage report',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/coverage/properties/:threshold
 * Get properties above specific coverage threshold
 */
router.get('/coverage/properties/:threshold', async (req, res) => {
  try {
    const { coverageTracker } = req.app.locals;
    const threshold = parseFloat(req.params.threshold) || 50;
    
    const properties = coverageTracker.getPropertiesAboveThreshold(threshold);
    
    res.json({
      success: true,
      data: {
        threshold,
        count: properties.length,
        properties
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Coverage properties analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to get coverage properties',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/accuracy
 * Get price accuracy analytics
 */
router.get('/accuracy', async (req, res) => {
  try {
    const { accuracyMonitor } = req.app.locals;
    const report = accuracyMonitor.generateAccuracyReport();
    
    res.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Accuracy analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to generate accuracy report',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/accuracy/trend
 * Get daily accuracy trend
 */
router.get('/accuracy/trend', async (req, res) => {
  try {
    const { accuracyMonitor } = req.app.locals;
    const days = parseInt(req.query.days) || 7;
    
    const trend = accuracyMonitor.getDailyAccuracyTrend(days);
    
    res.json({
      success: true,
      data: {
        days,
        trend
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Accuracy trend analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to get accuracy trend',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/updates
 * Get price update statistics
 */
router.get('/updates', async (req, res) => {
  try {
    const { updateScheduler } = req.app.locals;
    const stats = updateScheduler.getUpdateStats();
    
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Update analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to get update statistics',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /analytics/updates/force
 * Force update specific properties
 */
router.post('/updates/force', async (req, res) => {
  try {
    const { updateScheduler } = req.app.locals;
    const { propertyIds } = req.body;
    
    if (!propertyIds || !Array.isArray(propertyIds)) {
      return res.status(400).json({
        success: false,
        error: 'propertyIds array is required',
        timestamp: new Date().toISOString()
      });
    }
    
    // Start force update (don't wait for completion)
    updateScheduler.forceUpdateProperties(propertyIds).catch(error => {
      logger.error('Force update failed', { error: error.message, propertyIds });
    });
    
    res.json({
      success: true,
      message: `Force update initiated for ${propertyIds.length} properties`,
      propertyIds,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Force update error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to initiate force update',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/dashboard
 * Get comprehensive dashboard data
 */
router.get('/dashboard', async (req, res) => {
  try {
    const { coverageTracker, accuracyMonitor, updateScheduler } = req.app.locals;
    
    const coverageReport = coverageTracker.generateCoverageReport();
    const accuracyReport = accuracyMonitor.generateAccuracyReport();
    const updateStats = updateScheduler.getUpdateStats();
    const accuracyTrend = accuracyMonitor.getDailyAccuracyTrend(7);
    
    const dashboard = {
      summary: {
        totalProperties: coverageReport.summary.totalProperties,
        propertiesAbove50Percent: coverageReport.propertiesAbove50Percent.length,
        overallCoverage: coverageReport.summary.overallCoverage,
        overallAccuracy: accuracyReport.summary.overallAccuracy,
        lastUpdateTime: updateStats.lastUpdateTime,
        updateSuccessRate: updateStats.totalUpdates > 0 ? 
          (updateStats.successfulUpdates / updateStats.totalUpdates * 100).toFixed(2) : 0
      },
      coverage: {
        propertiesAbove50: coverageReport.propertiesAbove50Percent.slice(0, 10),
        propertiesAbove70: coverageReport.propertiesAbove70Percent.slice(0, 10),
        lowCoverageProperties: coverageReport.lowCoverageProperties.slice(0, 10)
      },
      accuracy: {
        excellentProperties: accuracyReport.summary.propertiesWithExcellentAccuracy,
        goodProperties: accuracyReport.summary.propertiesWithGoodAccuracy,
        poorProperties: accuracyReport.summary.propertiesWithPoorAccuracy,
        trend: accuracyTrend
      },
      recommendations: [
        ...coverageReport.recommendations,
        ...accuracyReport.recommendations
      ].slice(0, 10),
      googleRequirements: {
        propertiesAbove50Required: 5,
        propertiesAbove50Current: coverageReport.propertiesAbove50Percent.length,
        accuracyRequired: 95,
        accuracyCurrent: parseFloat(accuracyReport.summary.overallAccuracy),
        status: coverageReport.propertiesAbove50Percent.length >= 5 && 
                parseFloat(accuracyReport.summary.overallAccuracy) >= 95 ? 'READY' : 'NEEDS_IMPROVEMENT'
      }
    };
    
    res.json({
      success: true,
      data: dashboard,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Dashboard analytics error', { error: error.message });
    res.status(500).json({
      success: false,
      error: 'Failed to generate dashboard data',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /analytics/property/:propertyId
 * Get detailed analytics for a specific property
 */
router.get('/property/:propertyId', async (req, res) => {
  try {
    const { coverageTracker, accuracyMonitor } = req.app.locals;
    const { propertyId } = req.params;
    
    const coverage = coverageTracker.calculatePropertyCoverage(propertyId);
    const propertyData = coverageTracker.coverageData.get(propertyId);
    const accuracyData = accuracyMonitor.accuracyData.get(propertyId);
    
    if (!propertyData) {
      return res.status(404).json({
        success: false,
        error: 'Property not found',
        timestamp: new Date().toISOString()
      });
    }
    
    const analytics = {
      propertyId,
      coverage: {
        percentage: coverage.toFixed(2),
        totalQueries: propertyData.totalQueries,
        successfulResponses: propertyData.successfulResponses,
        lastQueried: propertyData.lastQueried,
        lastSuccessfulResponse: propertyData.lastSuccessfulResponse,
        itineraryTypes: Array.from(propertyData.itineraryTypes),
        deviceTypes: Array.from(propertyData.deviceTypes),
        countries: Array.from(propertyData.countries)
      },
      accuracy: accuracyData ? {
        averageAccuracy: accuracyData.averageAccuracy.toFixed(2),
        totalValidations: accuracyData.totalValidations,
        accurateCount: accuracyData.accurateCount,
        acceptableCount: accuracyData.acceptableCount,
        inaccurateCount: accuracyData.inaccurateCount,
        lastValidation: accuracyData.lastValidation,
        recentIssues: accuracyData.issues.slice(-5)
      } : null
    };
    
    res.json({
      success: true,
      data: analytics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Property analytics error', { error: error.message, propertyId: req.params.propertyId });
    res.status(500).json({
      success: false,
      error: 'Failed to get property analytics',
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
