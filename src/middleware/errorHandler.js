const logger = require('../utils/logger');

/**
 * Global error handler middleware
 */
function errorHandler(err, req, res, next) {
  logger.error('Unhandled error', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    headers: req.headers,
    body: req.body
  });

  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const errorResponse = {
    error: 'Internal Server Error',
    message: isDevelopment ? err.message : 'An unexpected error occurred',
    timestamp: new Date().toISOString()
  };

  if (isDevelopment) {
    errorResponse.stack = err.stack;
  }

  res.status(500).json(errorResponse);
}

/**
 * 404 handler for unknown routes
 */
function notFoundHandler(req, res) {
  logger.warn('Route not found', {
    url: req.url,
    method: req.method,
    userAgent: req.headers['user-agent']
  });

  res.status(404).json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    timestamp: new Date().toISOString()
  });
}

/**
 * Request timeout handler
 */
function timeoutHandler(timeout = 100000) {
  return (req, res, next) => {
    res.setTimeout(timeout, () => {
      logger.error('Request timeout', {
        url: req.url,
        method: req.method,
        timeout: `${timeout}ms`
      });

      if (!res.headersSent) {
        res.status(408).json({
          error: 'Request Timeout',
          message: 'The request took too long to process',
          timestamp: new Date().toISOString()
        });
      }
    });
    next();
  };
}

/**
 * Request validation middleware
 */
function validateRequest(req, res, next) {
  // Validate Content-Type for XML endpoints
  if (req.path === '/query' && req.method === 'POST') {
    const contentType = req.headers['content-type'];
    if (!contentType || !contentType.includes('application/xml')) {
      logger.warn('Invalid Content-Type for query endpoint', {
        contentType,
        userAgent: req.headers['user-agent']
      });
      
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content-Type must be application/xml for query endpoint',
        timestamp: new Date().toISOString()
      });
    }
  }

  next();
}

/**
 * IP whitelist middleware (optional security)
 */
function ipWhitelist(allowedIps) {
  return (req, res, next) => {
    if (!allowedIps || allowedIps.length === 0) {
      return next();
    }

    const clientIp = req.ip || req.connection.remoteAddress;
    const isAllowed = allowedIps.some(allowedIp => {
      // Simple IP matching - in production, use a proper CIDR library
      return clientIp.includes(allowedIp.split('/')[0]);
    });

    if (!isAllowed) {
      logger.warn('IP not whitelisted', {
        clientIp,
        userAgent: req.headers['user-agent']
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied',
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
}

/**
 * Request logging middleware
 */
function requestLogger(req, res, next) {
  const start = Date.now();
  
  // Log request
  logger.info('Incoming request', {
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    contentType: req.headers['content-type'],
    contentLength: req.headers['content-length'],
    ip: req.ip
  });

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: res.get('content-length') || 'unknown'
    });
  });

  next();
}

/**
 * Rate limiting middleware (basic implementation)
 */
function rateLimit(maxRequests = 100, windowMs = 60000) {
  const requests = new Map();

  return (req, res, next) => {
    const clientIp = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [ip, timestamps] of requests.entries()) {
      requests.set(ip, timestamps.filter(time => time > windowStart));
      if (requests.get(ip).length === 0) {
        requests.delete(ip);
      }
    }

    // Check current client
    const clientRequests = requests.get(clientIp) || [];
    
    if (clientRequests.length >= maxRequests) {
      logger.warn('Rate limit exceeded', {
        clientIp,
        requestCount: clientRequests.length,
        maxRequests
      });

      return res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        retryAfter: Math.ceil(windowMs / 1000),
        timestamp: new Date().toISOString()
      });
    }

    // Add current request
    clientRequests.push(now);
    requests.set(clientIp, clientRequests);

    next();
  };
}

module.exports = {
  errorHandler,
  notFoundHandler,
  timeoutHandler,
  validateRequest,
  ipWhitelist,
  requestLogger,
  rateLimit
};
