const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const config = require('./config/config');
const logger = require('./utils/logger');
const HotelCenterController = require('./controllers/hotelCenterController');
const {
  errorHandler,
  notFound<PERSON>andler,
  timeoutHandler,
  validateRequest,
  ipWhitelist,
  requestLogger,
  rateLimit
} = require('./middleware/errorHandler');

class GoogleHotelCenterServer {
  constructor() {
    this.app = express();
    this.hotelController = new HotelCenterController(config);
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: config.security.httpsOnly ? /^https:/ : true,
      methods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type', 'Authorization', 'User-Agent']
    }));

    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', true);

    // Request logging
    this.app.use(requestLogger);

    // Rate limiting
    this.app.use(rateLimit(
      config.supplierApi.rateLimit.maxRequests,
      config.supplierApi.rateLimit.windowMs
    ));

    // IP whitelist (if enabled)
    if (config.security.enableIpWhitelist) {
      this.app.use(ipWhitelist(config.security.allowedIps));
    }

    // Request timeout
    this.app.use(timeoutHandler(config.googleHotelCenter.maxResponseTime));

    // Request validation
    this.app.use(validateRequest);

    // Body parsing for XML
    this.app.use('/query', express.text({ type: 'application/xml', limit: '10mb' }));
    
    // JSON parsing for other endpoints
    this.app.use(express.json({ limit: '1mb' }));
  }

  setupRoutes() {
    // Main Google Hotel Center endpoint
    this.app.post('/query', async (req, res) => {
      await this.hotelController.handleQuery(req, res);
    });

    // Health check endpoint
    this.app.get(config.monitoring.healthEndpoint, async (req, res) => {
      await this.hotelController.healthCheck(req, res);
    });

    // Metrics endpoint (basic implementation)
    if (config.monitoring.enabled) {
      this.app.get(config.monitoring.metricsEndpoint, (req, res) => {
        res.json({
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          timestamp: new Date().toISOString(),
          version: require('../package.json').version
        });
      });
    }

    // Root endpoint with basic info
    this.app.get('/', (req, res) => {
      res.json({
        service: 'Google Hotel Center Integration',
        version: require('../package.json').version,
        status: 'running',
        endpoints: {
          query: '/query',
          health: config.monitoring.healthEndpoint,
          metrics: config.monitoring.enabled ? config.monitoring.metricsEndpoint : null
        },
        timestamp: new Date().toISOString()
      });
    });
  }

  setupErrorHandling() {
    // 404 handler
    this.app.use(notFoundHandler);
    
    // Global error handler
    this.app.use(errorHandler);
  }

  start() {
    return new Promise((resolve, reject) => {
      try {
        const server = this.app.listen(config.server.port, config.server.host, () => {
          logger.info('Google Hotel Center Integration Server started', {
            port: config.server.port,
            host: config.server.host,
            environment: config.server.environment,
            endpoints: {
              query: '/query',
              health: config.monitoring.healthEndpoint
            }
          });
          resolve(server);
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
          logger.info('SIGTERM received, shutting down gracefully');
          server.close(() => {
            logger.info('Server closed');
            process.exit(0);
          });
        });

        process.on('SIGINT', () => {
          logger.info('SIGINT received, shutting down gracefully');
          server.close(() => {
            logger.info('Server closed');
            process.exit(0);
          });
        });

      } catch (error) {
        logger.error('Failed to start server', { error: error.message });
        reject(error);
      }
    });
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new GoogleHotelCenterServer();
  server.start().catch(error => {
    logger.error('Failed to start application', { error: error.message });
    process.exit(1);
  });
}

module.exports = GoogleHotelCenterServer;
