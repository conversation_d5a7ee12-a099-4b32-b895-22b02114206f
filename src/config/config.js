require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0',
    environment: process.env.NODE_ENV || 'development'
  },

  // Google Hotel Center configuration
  googleHotelCenter: {
    // Your partner key from Google Hotel Center
    partnerKey: process.env.GHC_PARTNER_KEY,
    
    // Maximum response time for queries (in milliseconds)
    maxResponseTime: parseInt(process.env.GHC_MAX_RESPONSE_TIME) || 100000,
    
    // Maximum response time for live pricing queries (in milliseconds)
    livePricingMaxResponseTime: parseInt(process.env.GHC_LIVE_PRICING_MAX_RESPONSE_TIME) || 4000,
    
    // Default currency
    defaultCurrency: process.env.GHC_DEFAULT_CURRENCY || 'USD',
    
    // Your site ID for allowable points of sale
    siteId: process.env.GHC_SITE_ID || 'your_site_id',
    
    // Maximum advance purchase days (how far in advance bookings are allowed)
    maxAdvancePurchase: parseInt(process.env.GHC_MAX_ADVANCE_PURCHASE) || 330,
    
    // Maximum length of stay
    maxLengthOfStay: parseInt(process.env.GHC_MAX_LENGTH_OF_STAY) || 30
  },

  // Third-party supplier API configuration
  supplierApi: {
    baseURL: process.env.SUPPLIER_API_BASE_URL || 'https://api.your-supplier.com/v1',
    apiKey: process.env.SUPPLIER_API_KEY,
    timeout: parseInt(process.env.SUPPLIER_API_TIMEOUT) || 30000,
    retryAttempts: parseInt(process.env.SUPPLIER_API_RETRY_ATTEMPTS) || 3,
    
    // Rate limiting
    rateLimit: {
      maxRequests: parseInt(process.env.SUPPLIER_API_MAX_REQUESTS) || 100,
      windowMs: parseInt(process.env.SUPPLIER_API_WINDOW_MS) || 60000
    },
    
    // Endpoints
    endpoints: {
      prices: process.env.SUPPLIER_API_PRICES_ENDPOINT || '/prices/search',
      metadata: process.env.SUPPLIER_API_METADATA_ENDPOINT || '/hotels/metadata',
      health: process.env.SUPPLIER_API_HEALTH_ENDPOINT || '/health'
    }
  },

  // Hotel mapping configuration
  hotelMapping: {
    // Enable hotel ID mapping if your internal IDs differ from Google's
    enableMapping: process.env.ENABLE_HOTEL_MAPPING === 'true',
    
    // Mapping file path (JSON file with Google ID -> Your ID mappings)
    mappingFilePath: process.env.HOTEL_MAPPING_FILE_PATH || './data/hotel-mappings.json',
    
    // Default room and package IDs
    defaultRoomId: process.env.DEFAULT_ROOM_ID || 'standard',
    defaultPackageId: process.env.DEFAULT_PACKAGE_ID || 'base'
  },

  // Caching configuration
  cache: {
    // Enable response caching
    enabled: process.env.CACHE_ENABLED === 'true',
    
    // Cache TTL in seconds
    ttl: parseInt(process.env.CACHE_TTL) || 300,
    
    // Redis configuration (if using Redis for caching)
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB) || 0
    }
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
    
    // File logging
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      filename: process.env.LOG_FILE_PATH || './logs/ghc-integration.log',
      maxSize: process.env.LOG_MAX_SIZE || '10m',
      maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
    },
    
    // Console logging
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false'
    }
  },

  // Security configuration
  security: {
    // Enable IP whitelisting for Google's IPs
    enableIpWhitelist: process.env.ENABLE_IP_WHITELIST === 'true',
    
    // Google's IP ranges (update these based on Google's documentation)
    allowedIps: process.env.ALLOWED_IPS ? process.env.ALLOWED_IPS.split(',') : [
      '***********/20',
      '************/19',
      '***********/18',
      '************/17',
      '************/19'
    ],
    
    // API key for your endpoints (if needed)
    apiKey: process.env.API_KEY,
    
    // Enable HTTPS only
    httpsOnly: process.env.HTTPS_ONLY === 'true'
  },

  // Monitoring and metrics
  monitoring: {
    // Enable metrics collection
    enabled: process.env.MONITORING_ENABLED === 'true',
    
    // Metrics endpoint
    metricsEndpoint: process.env.METRICS_ENDPOINT || '/metrics',
    
    // Health check endpoint
    healthEndpoint: process.env.HEALTH_ENDPOINT || '/health'
  },

  // Business logic configuration
  business: {
    // Default markup percentage on supplier prices
    defaultMarkup: parseFloat(process.env.DEFAULT_MARKUP) || 0.0,
    
    // Minimum price threshold
    minimumPrice: parseFloat(process.env.MINIMUM_PRICE) || 1.0,
    
    // Maximum price threshold
    maximumPrice: parseFloat(process.env.MAXIMUM_PRICE) || 10000.0,
    
    // Default refund policy
    defaultRefundPolicy: {
      available: process.env.DEFAULT_REFUND_AVAILABLE === 'true',
      refundableUntilDays: parseInt(process.env.DEFAULT_REFUND_DAYS) || 1,
      refundableUntilTime: process.env.DEFAULT_REFUND_TIME || '18:00:00'
    },
    
    // Default amenities
    defaultAmenities: {
      breakfastIncluded: process.env.DEFAULT_BREAKFAST === 'true',
      internetIncluded: process.env.DEFAULT_INTERNET === 'true',
      parkingIncluded: process.env.DEFAULT_PARKING === 'true'
    }
  }
};

// Validation
function validateConfig() {
  const required = [
    'SUPPLIER_API_BASE_URL',
    'SUPPLIER_API_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate URLs
  try {
    new URL(config.supplierApi.baseURL);
  } catch (error) {
    throw new Error('Invalid SUPPLIER_API_BASE_URL');
  }
}

// Validate configuration on load
if (process.env.NODE_ENV !== 'test') {
  validateConfig();
}

module.exports = config;
