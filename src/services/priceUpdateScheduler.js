const logger = require('../utils/logger');
const cron = require('node-cron');

/**
 * Price Update Scheduler
 * Manages frequent price updates to maintain accuracy and freshness
 */
class PriceUpdateScheduler {
  constructor(supplierClient, config) {
    this.supplierClient = supplierClient;
    this.config = config;
    this.priceCache = new Map();
    this.updateQueue = new Set();
    this.isUpdating = false;
    this.updateStats = {
      totalUpdates: 0,
      successfulUpdates: 0,
      failedUpdates: 0,
      lastUpdateTime: null,
      averageUpdateDuration: 0
    };

    // Start scheduled updates
    this.startScheduledUpdates();
  }

  /**
   * Start scheduled price updates
   */
  startScheduledUpdates() {
    // High-frequency updates for popular properties (every 15 minutes)
    cron.schedule('*/15 * * * *', async () => {
      await this.updateHighPriorityProperties();
    });

    // Medium-frequency updates for all properties (every hour)
    cron.schedule('0 * * * *', async () => {
      await this.updateAllProperties();
    });

    // Cache cleanup (every 6 hours)
    cron.schedule('0 */6 * * *', async () => {
      await this.cleanupCache();
    });

    // Daily statistics report
    cron.schedule('0 9 * * *', async () => {
      await this.generateDailyReport();
    });

    logger.info('Price update scheduler started', {
      highFrequencyUpdates: 'Every 15 minutes',
      allPropertiesUpdate: 'Every hour',
      cacheCleanup: 'Every 6 hours'
    });
  }

  /**
   * Update high-priority properties more frequently
   */
  async updateHighPriorityProperties() {
    if (this.isUpdating) {
      logger.debug('Update already in progress, skipping high-priority update');
      return;
    }

    try {
      this.isUpdating = true;
      const startTime = Date.now();

      // Get high-priority properties (most queried, best performing)
      const highPriorityProperties = this.getHighPriorityProperties();

      if (highPriorityProperties.length === 0) {
        logger.debug('No high-priority properties identified');
        return;
      }

      logger.info('Starting high-priority price update', {
        propertyCount: highPriorityProperties.length
      });

      // Update prices for high-priority properties
      await this.updatePropertiesPrices(highPriorityProperties, 'high-priority');

      const duration = Date.now() - startTime;
      logger.info('High-priority price update completed', {
        duration: `${duration}ms`,
        properties: highPriorityProperties.length
      });

    } catch (error) {
      logger.error('High-priority price update failed', { error: error.message });
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Update all properties
   */
  async updateAllProperties() {
    if (this.isUpdating) {
      logger.debug('Update already in progress, skipping all-properties update');
      return;
    }

    try {
      this.isUpdating = true;
      const startTime = Date.now();

      // Get all properties that need updates
      const allProperties = this.getAllPropertiesForUpdate();

      if (allProperties.length === 0) {
        logger.debug('No properties need updates');
        return;
      }

      logger.info('Starting all-properties price update', {
        propertyCount: allProperties.length
      });

      // Update in batches to avoid overwhelming the supplier API
      const batchSize = 50;
      for (let i = 0; i < allProperties.length; i += batchSize) {
        const batch = allProperties.slice(i, i + batchSize);
        await this.updatePropertiesPrices(batch, 'batch');

        // Small delay between batches
        if (i + batchSize < allProperties.length) {
          await this.sleep(1000);
        }
      }

      const duration = Date.now() - startTime;
      this.updateStats.lastUpdateTime = new Date().toISOString();
      this.updateStats.averageUpdateDuration = duration;

      logger.info('All-properties price update completed', {
        duration: `${duration}ms`,
        properties: allProperties.length,
        batches: Math.ceil(allProperties.length / batchSize)
      });

    } catch (error) {
      logger.error('All-properties price update failed', { error: error.message });
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Update prices for specific properties
   */
  async updatePropertiesPrices(propertyIds, updateType = 'manual') {
    const updatePromises = [];
    const checkinDate = this.getNextCheckinDate();
    const nights = 1; // Start with 1-night stays for frequent updates

    // Create update requests for different scenarios to maximize price coverage
    const scenarios = [
      // Standard scenarios
      { nights: 1, adults: 2, children: [], includeAllOccupancies: true },
      { nights: 2, adults: 2, children: [], includeAllOccupancies: true },
      { nights: 3, adults: 2, children: [], includeAllOccupancies: true },
      { nights: 7, adults: 2, children: [], includeAllOccupancies: false }, // Weekly stays

      // Different occupancies
      { nights: 1, adults: 1, children: [], includeAllOccupancies: false }, // Single
      { nights: 2, adults: 3, children: [], includeAllOccupancies: false }, // Triple
      { nights: 2, adults: 4, children: [], includeAllOccupancies: false }, // Quad

      // Family scenarios
      { nights: 2, adults: 2, children: [{ age: 8 }], includeAllOccupancies: false },
      { nights: 3, adults: 2, children: [{ age: 6 }, { age: 10 }], includeAllOccupancies: false },

      // Business travel (advance dates)
      { nights: 1, adults: 1, children: [], advanceDays: 7, includeAllOccupancies: false },
      { nights: 2, adults: 1, children: [], advanceDays: 14, includeAllOccupancies: false },

      // Weekend scenarios
      { nights: 2, adults: 2, children: [], weekend: true, includeAllOccupancies: false },
      { nights: 3, adults: 2, children: [], weekend: true, includeAllOccupancies: false }
    ];

    for (const scenario of scenarios) {
      // Calculate check-in date based on scenario
      let scenarioCheckinDate = checkinDate;
      if (scenario.advanceDays) {
        const advanceDate = new Date();
        advanceDate.setDate(advanceDate.getDate() + scenario.advanceDays);
        scenarioCheckinDate = advanceDate.toISOString().split('T')[0];
      } else if (scenario.weekend) {
        // Find next Friday for weekend scenarios
        scenarioCheckinDate = this.getNextWeekendDate();
      }

      const priceRequest = {
        hotelIds: propertyIds,
        checkin: scenarioCheckinDate,
        nights: scenario.nights,
        occupancy: {
          adults: scenario.adults,
          children: scenario.children
        },
        currency: 'USD',
        updateType: updateType,
        includeAllOccupancies: scenario.includeAllOccupancies,
        // Add scenario context for better supplier API requests
        scenarioType: this.getScenarioType(scenario)
      };

      updatePromises.push(
        this.updateSingleScenario(priceRequest).catch(error => {
          logger.error('Scenario update failed', {
            scenario,
            error: error.message,
            propertyCount: propertyIds.length
          });
          return null;
        })
      );
    }

    const results = await Promise.all(updatePromises);
    const successfulUpdates = results.filter(r => r !== null).length;

    this.updateStats.totalUpdates += scenarios.length;
    this.updateStats.successfulUpdates += successfulUpdates;
    this.updateStats.failedUpdates += (scenarios.length - successfulUpdates);

    logger.debug('Property prices updated', {
      properties: propertyIds.length,
      scenarios: scenarios.length,
      successful: successfulUpdates,
      updateType
    });
  }

  /**
   * Update prices for a single scenario
   */
  async updateSingleScenario(priceRequest) {
    try {
      const priceData = await this.supplierClient.fetchHotelPrices(priceRequest);

      // Cache the updated prices
      const cacheKey = this.generateCacheKey(priceRequest);
      this.priceCache.set(cacheKey, {
        data: priceData,
        timestamp: Date.now(),
        updateType: priceRequest.updateType
      });

      return priceData;
    } catch (error) {
      logger.error('Failed to update scenario prices', {
        checkin: priceRequest.checkin,
        nights: priceRequest.nights,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get high-priority properties for frequent updates
   */
  getHighPriorityProperties() {
    // This should be based on your actual data
    // For now, return a sample set of high-performing properties
    const highPriorityProperties = [
      // Add your most queried/important hotel IDs here
      'hotel_001', 'hotel_002', 'hotel_003', 'hotel_004', 'hotel_005',
      'hotel_010', 'hotel_015', 'hotel_020', 'hotel_025', 'hotel_030'
    ];

    return highPriorityProperties;
  }

  /**
   * Get all properties that need updates
   */
  getAllPropertiesForUpdate() {
    // This should return all your hotel IDs
    // For now, return a sample set
    const allProperties = [];

    // Generate sample hotel IDs (replace with your actual hotel list)
    for (let i = 1; i <= 200; i++) {
      allProperties.push(`hotel_${i.toString().padStart(3, '0')}`);
    }

    return allProperties;
  }

  /**
   * Get next check-in date (tomorrow)
   */
  getNextCheckinDate() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  }

  /**
   * Generate cache key for price data
   */
  generateCacheKey(priceRequest) {
    const occupancyKey = `${priceRequest.occupancy.adults}-${priceRequest.occupancy.children.length}`;
    return `${priceRequest.checkin}-${priceRequest.nights}-${occupancyKey}-${priceRequest.hotelIds.join(',')}`;
  }

  /**
   * Get cached price data
   */
  getCachedPrices(priceRequest) {
    const cacheKey = this.generateCacheKey(priceRequest);
    const cached = this.priceCache.get(cacheKey);

    if (!cached) return null;

    // Check if cache is still fresh (15 minutes for high-priority, 1 hour for others)
    const maxAge = this.isHighPriorityRequest(priceRequest) ? 15 * 60 * 1000 : 60 * 60 * 1000;
    const age = Date.now() - cached.timestamp;

    if (age > maxAge) {
      this.priceCache.delete(cacheKey);
      return null;
    }

    return cached.data;
  }

  /**
   * Check if request is for high-priority properties
   */
  isHighPriorityRequest(priceRequest) {
    const highPriorityProperties = this.getHighPriorityProperties();
    return priceRequest.hotelIds.some(id => highPriorityProperties.includes(id));
  }

  /**
   * Clean up old cache entries
   */
  async cleanupCache() {
    const now = Date.now();
    const maxAge = 6 * 60 * 60 * 1000; // 6 hours
    let cleanedCount = 0;

    for (const [key, cached] of this.priceCache.entries()) {
      if (now - cached.timestamp > maxAge) {
        this.priceCache.delete(key);
        cleanedCount++;
      }
    }

    logger.info('Cache cleanup completed', {
      cleanedEntries: cleanedCount,
      remainingEntries: this.priceCache.size
    });
  }

  /**
   * Generate daily update report
   */
  async generateDailyReport() {
    const report = {
      date: new Date().toISOString().split('T')[0],
      updateStats: { ...this.updateStats },
      cacheStats: {
        totalEntries: this.priceCache.size,
        memoryUsage: this.estimateCacheMemoryUsage()
      },
      recommendations: []
    };

    // Calculate success rate
    const successRate = this.updateStats.totalUpdates > 0
      ? (this.updateStats.successfulUpdates / this.updateStats.totalUpdates * 100).toFixed(2)
      : 0;

    report.updateStats.successRate = parseFloat(successRate);

    // Generate recommendations
    if (successRate < 90) {
      report.recommendations.push({
        priority: 'HIGH',
        action: 'Price update success rate is low',
        details: `Only ${successRate}% of updates successful. Check supplier API reliability.`
      });
    }

    if (this.priceCache.size > 10000) {
      report.recommendations.push({
        priority: 'MEDIUM',
        action: 'Cache size is large',
        details: 'Consider reducing cache TTL or implementing more aggressive cleanup.'
      });
    }

    logger.info('Daily price update report', report);
    return report;
  }

  /**
   * Estimate cache memory usage
   */
  estimateCacheMemoryUsage() {
    // Rough estimate - each cache entry is approximately 2KB
    return `${(this.priceCache.size * 2).toFixed(1)}KB`;
  }

  /**
   * Force update specific properties
   */
  async forceUpdateProperties(propertyIds) {
    logger.info('Force updating properties', { properties: propertyIds });
    await this.updatePropertiesPrices(propertyIds, 'force');
  }

  /**
   * Get update statistics
   */
  getUpdateStats() {
    return {
      ...this.updateStats,
      cacheSize: this.priceCache.size,
      isUpdating: this.isUpdating
    };
  }

  /**
   * Get next weekend date (Friday)
   */
  getNextWeekendDate() {
    const today = new Date();
    const friday = new Date(today);
    friday.setDate(today.getDate() + (5 - today.getDay() + 7) % 7);
    return friday.toISOString().split('T')[0];
  }

  /**
   * Determine scenario type for supplier API optimization
   */
  getScenarioType(scenario) {
    if (scenario.advanceDays) return 'advance_booking';
    if (scenario.weekend) return 'weekend';
    if (scenario.adults === 1) return 'business';
    if (scenario.children && scenario.children.length > 0) return 'family';
    if (scenario.nights >= 7) return 'extended_stay';
    return 'standard';
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = PriceUpdateScheduler;
