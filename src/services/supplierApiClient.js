const axios = require('axios');
const logger = require('../utils/logger');

class SupplierApiClient {
  constructor(config) {
    this.baseURL = config.supplierApi.baseURL;
    this.apiKey = config.supplierApi.apiKey;
    this.timeout = config.supplierApi.timeout || 30000;
    this.retryAttempts = config.supplierApi.retryAttempts || 3;

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'User-Agent': 'GoogleHotelCenter-Integration/1.0'
      }
    });

    // Add request/response interceptors for logging
    this.setupInterceptors();
  }

  /**
   * Fetch hotel prices from third-party supplier API
   * @param {Object} request - Price request object
   * @returns {Object} Price response from supplier
   */
  async fetchHotelPrices(request) {
    const startTime = Date.now();

    try {
      logger.info('Fetching prices from supplier API', {
        hotelIds: request.hotelIds,
        checkin: request.checkin,
        nights: request.nights,
        occupancy: request.occupancy
      });

      const response = await this.makeRequestWithRetry('/prices/search', {
        method: 'POST',
        data: this.buildSupplierRequest(request)
      });

      const duration = Date.now() - startTime;
      logger.info('Supplier API response received', {
        duration: `${duration}ms`,
        hotelCount: response.data?.hotels?.length || 0
      });

      return this.parseSupplierResponse(response.data);
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Supplier API request failed', {
        error: error.message,
        duration: `${duration}ms`,
        hotelIds: request.hotelIds
      });
      throw error;
    }
  }

  /**
   * Build request payload for supplier API
   */
  buildSupplierRequest(request) {
    return {
      hotel_ids: request.hotelIds,
      check_in: request.checkin,
      check_out: this.calculateCheckout(request.checkin, request.nights),
      nights: request.nights,
      occupancy: {
        adults: request.occupancy?.adults || 2,
        children: request.occupancy?.children || []
      },
      currency: request.currency || 'USD',
      user_country: request.userCountry || 'US',
      device_type: request.userDevice || 'desktop',
      // Enhanced: Request multiple rate variations
      include_taxes: true,
      include_fees: true,
      rate_type: 'public',
      // Request multiple room types and packages
      include_all_rooms: true,
      include_all_packages: true,
      include_all_occupancies: request.includeAllOccupancies || false,
      // Request different rate plans
      rate_plans: ['standard', 'flexible', 'advance_purchase', 'mobile_exclusive'],
      // Request different meal plans
      meal_plans: ['room_only', 'breakfast', 'half_board', 'full_board']
    };
  }

  /**
   * Parse supplier API response into standardized format
   */
  parseSupplierResponse(supplierData) {
    if (!supplierData || !supplierData.hotels) {
      return { hotels: [] };
    }

    const hotels = supplierData.hotels.map(hotel => ({
      hotelId: hotel.hotel_id || hotel.id,
      available: hotel.available !== false,
      rates: hotel.rates ? hotel.rates.map(rate => ({
        roomId: rate.room_id || 'default',
        packageId: rate.package_id || 'standard',
        baseRate: parseFloat(rate.base_rate || rate.price),
        currency: rate.currency || 'USD',
        taxes: parseFloat(rate.taxes || 0),
        fees: parseFloat(rate.fees || 0),
        totalPrice: parseFloat(rate.total_price || (rate.base_rate + rate.taxes + rate.fees)),
        refundable: rate.refundable || false,
        refundableUntilDays: rate.refundable_until_days || 0,
        breakfastIncluded: rate.breakfast_included || false,
        internetIncluded: rate.internet_included || false,
        parkingIncluded: rate.parking_included || false,
        occupancy: rate.occupancy || 2,
        ratePlanId: rate.rate_plan_id || `${rate.room_id}-${rate.package_id}`,
        restrictions: rate.restrictions || {}
      })) : [],
      lastUpdated: hotel.last_updated || new Date().toISOString(),
      supplier: hotel.supplier || 'default'
    }));

    return { hotels };
  }

  /**
   * Calculate checkout date from checkin and nights
   */
  calculateCheckout(checkin, nights) {
    const checkinDate = new Date(checkin);
    const checkoutDate = new Date(checkinDate);
    checkoutDate.setDate(checkinDate.getDate() + nights);
    return checkoutDate.toISOString().split('T')[0];
  }

  /**
   * Make HTTP request with retry logic
   */
  async makeRequestWithRetry(endpoint, options, attempt = 1) {
    try {
      return await this.client.request({
        url: endpoint,
        ...options
      });
    } catch (error) {
      if (attempt < this.retryAttempts && this.isRetryableError(error)) {
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
        logger.warn(`Supplier API request failed, retrying in ${delay}ms`, {
          attempt,
          error: error.message
        });

        await this.sleep(delay);
        return this.makeRequestWithRetry(endpoint, options, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    if (!error.response) return true; // Network errors are retryable

    const status = error.response.status;
    return status >= 500 || status === 429; // Server errors and rate limits
  }

  /**
   * Sleep utility for retry delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Setup request/response interceptors
   */
  setupInterceptors() {
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Supplier API request', {
          method: config.method,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('Supplier API request error', { error: error.message });
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Supplier API response', {
          status: response.status,
          dataSize: JSON.stringify(response.data).length
        });
        return response;
      },
      (error) => {
        logger.error('Supplier API response error', {
          status: error.response?.status,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Fetch hotel metadata from supplier API (optional method)
   * @param {Object} request - Metadata request object
   * @returns {Object} Metadata response from supplier
   */
  async fetchHotelMetadata(request) {
    try {
      logger.info('Fetching metadata from supplier API', {
        hotelIds: request.hotelIds
      });

      const response = await this.makeRequestWithRetry('/hotels/metadata', {
        method: 'POST',
        data: {
          hotel_ids: request.hotelIds,
          include_rooms: request.includeRooms || true,
          include_packages: request.includePackages || true
        }
      });

      return this.parseSupplierMetadataResponse(response.data);
    } catch (error) {
      logger.error('Supplier metadata API request failed', {
        error: error.message,
        hotelIds: request.hotelIds
      });
      throw error;
    }
  }

  /**
   * Parse supplier metadata response
   */
  parseSupplierMetadataResponse(supplierData) {
    if (!supplierData || !supplierData.hotels) {
      return { hotels: [] };
    }

    const hotels = supplierData.hotels.map(hotel => ({
      hotelId: hotel.hotel_id || hotel.id,
      rooms: hotel.rooms?.map(room => ({
        roomId: room.room_id,
        name: room.name,
        description: room.description,
        capacity: room.capacity || 4,
        photoUrl: room.photo_url,
        photoCaption: room.photo_caption
      })) || [],
      packages: hotel.packages?.map(pkg => ({
        packageId: pkg.package_id,
        name: pkg.name,
        description: pkg.description,
        occupancy: pkg.occupancy || 2,
        chargeCurrency: pkg.charge_currency || 'web',
        breakfastIncluded: pkg.breakfast_included || false,
        internetIncluded: pkg.internet_included || false,
        parkingIncluded: pkg.parking_included || false,
        refundable: pkg.refundable || false,
        refundableUntilDays: pkg.refundable_until_days || 1
      })) || []
    }));

    return { hotels };
  }

  /**
   * Health check for supplier API
   */
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return {
        status: 'healthy',
        responseTime: response.headers['x-response-time'] || 'unknown'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

module.exports = SupplierApiClient;
