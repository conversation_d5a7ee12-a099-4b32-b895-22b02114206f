const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');

/**
 * Price Coverage Tracker
 * Monitors individual property price coverage to meet Google's requirements
 */
class PriceCoverageTracker {
  constructor(config) {
    this.config = config;
    this.coverageData = new Map();
    this.coverageFilePath = path.join(__dirname, '../../data/price-coverage.json');
    this.loadCoverageData();
    
    // Track coverage metrics
    this.metrics = {
      totalQueries: 0,
      successfulResponses: 0,
      propertiesWithPrices: new Set(),
      propertiesQueried: new Set(),
      dailyStats: new Map()
    };
  }

  /**
   * Track a query request for coverage analysis
   */
  async trackQuery(parsedQuery, responseData) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Update metrics
      this.metrics.totalQueries++;
      
      if (parsedQuery.properties) {
        parsedQuery.properties.forEach(propertyId => {
          this.metrics.propertiesQueried.add(propertyId);
          this.trackPropertyQuery(propertyId, parsedQuery, today);
        });
      }

      // Track successful responses
      if (responseData && responseData.hotels) {
        this.metrics.successfulResponses++;
        
        responseData.hotels.forEach(hotel => {
          if (hotel.available && hotel.rates && hotel.rates.length > 0) {
            this.metrics.propertiesWithPrices.add(hotel.hotelId);
            this.trackPropertyResponse(hotel.hotelId, hotel, parsedQuery, today);
          } else {
            this.trackPropertyNoPrice(hotel.hotelId, parsedQuery, today);
          }
        });
      }

      // Log coverage stats periodically
      if (this.metrics.totalQueries % 100 === 0) {
        await this.logCoverageStats();
      }

      // Save coverage data periodically
      if (this.metrics.totalQueries % 50 === 0) {
        await this.saveCoverageData();
      }

    } catch (error) {
      logger.error('Error tracking price coverage', { error: error.message });
    }
  }

  /**
   * Track individual property query
   */
  trackPropertyQuery(propertyId, query, date) {
    if (!this.coverageData.has(propertyId)) {
      this.coverageData.set(propertyId, {
        propertyId,
        totalQueries: 0,
        successfulResponses: 0,
        lastQueried: null,
        lastSuccessfulResponse: null,
        dailyStats: new Map(),
        itineraryTypes: new Set(),
        deviceTypes: new Set(),
        countries: new Set(),
        occupancyTypes: new Set()
      });
    }

    const propertyData = this.coverageData.get(propertyId);
    propertyData.totalQueries++;
    propertyData.lastQueried = new Date().toISOString();

    // Track query variations for coverage analysis
    const itineraryKey = `${query.checkin}-${query.nights}`;
    propertyData.itineraryTypes.add(itineraryKey);
    
    if (query.context?.userDevice) {
      propertyData.deviceTypes.add(query.context.userDevice);
    }
    
    if (query.context?.userCountry) {
      propertyData.countries.add(query.context.userCountry);
    }
    
    if (query.context?.occupancyDetails) {
      const occupancyKey = `${query.context.occupancyDetails.numAdults}-${query.context.occupancyDetails.children?.length || 0}`;
      propertyData.occupancyTypes.add(occupancyKey);
    }

    // Daily stats
    if (!propertyData.dailyStats.has(date)) {
      propertyData.dailyStats.set(date, {
        queries: 0,
        responses: 0,
        priceResponses: 0,
        noPriceResponses: 0
      });
    }
    propertyData.dailyStats.get(date).queries++;
  }

  /**
   * Track successful property response with prices
   */
  trackPropertyResponse(propertyId, hotel, query, date) {
    const propertyData = this.coverageData.get(propertyId);
    if (propertyData) {
      propertyData.successfulResponses++;
      propertyData.lastSuccessfulResponse = new Date().toISOString();
      
      const dailyStats = propertyData.dailyStats.get(date);
      if (dailyStats) {
        dailyStats.responses++;
        dailyStats.priceResponses++;
      }
    }
  }

  /**
   * Track property response without prices
   */
  trackPropertyNoPrice(propertyId, query, date) {
    const propertyData = this.coverageData.get(propertyId);
    if (propertyData) {
      const dailyStats = propertyData.dailyStats.get(date);
      if (dailyStats) {
        dailyStats.responses++;
        dailyStats.noPriceResponses++;
      }
    }
  }

  /**
   * Calculate price coverage percentage for a property
   */
  calculatePropertyCoverage(propertyId) {
    const propertyData = this.coverageData.get(propertyId);
    if (!propertyData || propertyData.totalQueries === 0) {
      return 0;
    }

    return (propertyData.successfulResponses / propertyData.totalQueries) * 100;
  }

  /**
   * Get properties with coverage above threshold
   */
  getPropertiesAboveThreshold(threshold = 50) {
    const qualifyingProperties = [];
    
    for (const [propertyId, data] of this.coverageData.entries()) {
      const coverage = this.calculatePropertyCoverage(propertyId);
      if (coverage >= threshold) {
        qualifyingProperties.push({
          propertyId,
          coverage: coverage.toFixed(2),
          totalQueries: data.totalQueries,
          successfulResponses: data.successfulResponses,
          lastQueried: data.lastQueried,
          lastSuccessfulResponse: data.lastSuccessfulResponse
        });
      }
    }

    return qualifyingProperties.sort((a, b) => b.coverage - a.coverage);
  }

  /**
   * Get detailed coverage report
   */
  generateCoverageReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalProperties: this.coverageData.size,
        propertiesQueried: this.metrics.propertiesQueried.size,
        propertiesWithPrices: this.metrics.propertiesWithPrices.size,
        totalQueries: this.metrics.totalQueries,
        successfulResponses: this.metrics.successfulResponses,
        overallCoverage: this.metrics.totalQueries > 0 ? 
          (this.metrics.successfulResponses / this.metrics.totalQueries * 100).toFixed(2) : 0
      },
      propertiesAbove50Percent: this.getPropertiesAboveThreshold(50),
      propertiesAbove70Percent: this.getPropertiesAboveThreshold(70),
      lowCoverageProperties: this.getLowCoverageProperties(),
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  /**
   * Get properties with low coverage that need attention
   */
  getLowCoverageProperties(threshold = 30) {
    const lowCoverageProperties = [];
    
    for (const [propertyId, data] of this.coverageData.entries()) {
      const coverage = this.calculatePropertyCoverage(propertyId);
      if (data.totalQueries >= 10 && coverage < threshold) {
        lowCoverageProperties.push({
          propertyId,
          coverage: coverage.toFixed(2),
          totalQueries: data.totalQueries,
          successfulResponses: data.successfulResponses,
          issuesIdentified: this.identifyPropertyIssues(propertyId)
        });
      }
    }

    return lowCoverageProperties.sort((a, b) => a.coverage - b.coverage);
  }

  /**
   * Identify potential issues with a property
   */
  identifyPropertyIssues(propertyId) {
    const propertyData = this.coverageData.get(propertyId);
    const issues = [];

    if (!propertyData) return issues;

    // Check if property hasn't been queried recently
    if (propertyData.lastQueried) {
      const lastQueriedDate = new Date(propertyData.lastQueried);
      const hoursSinceLastQuery = (Date.now() - lastQueriedDate.getTime()) / (1000 * 60 * 60);
      if (hoursSinceLastQuery > 24) {
        issues.push('Not queried in last 24 hours');
      }
    }

    // Check if property never had successful responses
    if (propertyData.successfulResponses === 0) {
      issues.push('Never returned prices');
    }

    // Check coverage diversity
    if (propertyData.itineraryTypes.size < 3) {
      issues.push('Limited itinerary coverage');
    }

    if (propertyData.deviceTypes.size < 2) {
      issues.push('Limited device type coverage');
    }

    return issues;
  }

  /**
   * Generate actionable recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    const qualifyingProperties = this.getPropertiesAboveThreshold(50);
    
    if (qualifyingProperties.length < 5) {
      recommendations.push({
        priority: 'HIGH',
        action: `Need ${5 - qualifyingProperties.length} more properties with >50% coverage`,
        details: 'Focus on improving supplier API response rates and ensuring price availability'
      });
    }

    const lowCoverageProps = this.getLowCoverageProperties();
    if (lowCoverageProps.length > 0) {
      recommendations.push({
        priority: 'MEDIUM',
        action: `${lowCoverageProps.length} properties have low coverage`,
        details: 'Review supplier API responses and hotel availability for these properties'
      });
    }

    if (this.metrics.successfulResponses / this.metrics.totalQueries < 0.7) {
      recommendations.push({
        priority: 'HIGH',
        action: 'Overall response rate is below 70%',
        details: 'Check supplier API reliability and error handling'
      });
    }

    return recommendations;
  }

  /**
   * Log coverage statistics
   */
  async logCoverageStats() {
    const report = this.generateCoverageReport();
    
    logger.info('Price Coverage Report', {
      totalProperties: report.summary.totalProperties,
      propertiesAbove50Percent: report.propertiesAbove50Percent.length,
      overallCoverage: report.summary.overallCoverage,
      totalQueries: report.summary.totalQueries,
      recommendations: report.recommendations.length
    });

    // Log specific properties that need attention
    if (report.lowCoverageProperties.length > 0) {
      logger.warn('Properties with low coverage', {
        count: report.lowCoverageProperties.length,
        properties: report.lowCoverageProperties.slice(0, 5).map(p => ({
          id: p.propertyId,
          coverage: p.coverage,
          issues: p.issuesIdentified
        }))
      });
    }
  }

  /**
   * Load coverage data from file
   */
  async loadCoverageData() {
    try {
      const data = await fs.readFile(this.coverageFilePath, 'utf8');
      const parsed = JSON.parse(data);
      
      // Convert back to Map with Set objects
      for (const [propertyId, propertyData] of Object.entries(parsed)) {
        propertyData.itineraryTypes = new Set(propertyData.itineraryTypes);
        propertyData.deviceTypes = new Set(propertyData.deviceTypes);
        propertyData.countries = new Set(propertyData.countries);
        propertyData.occupancyTypes = new Set(propertyData.occupancyTypes);
        propertyData.dailyStats = new Map(Object.entries(propertyData.dailyStats));
        
        this.coverageData.set(propertyId, propertyData);
      }
      
      logger.info('Price coverage data loaded', { 
        properties: this.coverageData.size 
      });
    } catch (error) {
      logger.info('No existing coverage data found, starting fresh');
    }
  }

  /**
   * Save coverage data to file
   */
  async saveCoverageData() {
    try {
      // Convert Map and Set objects to serializable format
      const serializable = {};
      for (const [propertyId, propertyData] of this.coverageData.entries()) {
        serializable[propertyId] = {
          ...propertyData,
          itineraryTypes: Array.from(propertyData.itineraryTypes),
          deviceTypes: Array.from(propertyData.deviceTypes),
          countries: Array.from(propertyData.countries),
          occupancyTypes: Array.from(propertyData.occupancyTypes),
          dailyStats: Object.fromEntries(propertyData.dailyStats)
        };
      }
      
      await fs.writeFile(this.coverageFilePath, JSON.stringify(serializable, null, 2));
      logger.debug('Price coverage data saved');
    } catch (error) {
      logger.error('Failed to save coverage data', { error: error.message });
    }
  }
}

module.exports = PriceCoverageTracker;
