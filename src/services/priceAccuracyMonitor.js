const logger = require('../utils/logger');
const axios = require('axios');

/**
 * Price Accuracy Monitor
 * Validates that prices sent to Google match your landing page prices
 */
class PriceAccuracyMonitor {
  constructor(config) {
    this.config = config;
    this.accuracyData = new Map();
    this.validationQueue = [];
    this.isProcessing = false;
    
    // Start validation processor
    this.startValidationProcessor();
  }

  /**
   * Queue price validation for accuracy checking
   */
  async queuePriceValidation(queryData, responseData, landingPageUrl) {
    try {
      if (!responseData.hotels || responseData.hotels.length === 0) {
        return;
      }

      for (const hotel of responseData.hotels) {
        if (hotel.available && hotel.rates && hotel.rates.length > 0) {
          for (const rate of hotel.rates) {
            this.validationQueue.push({
              timestamp: new Date().toISOString(),
              propertyId: hotel.hotelId,
              checkin: queryData.checkin,
              nights: queryData.nights,
              occupancy: queryData.context?.occupancyDetails || { adults: 2, children: [] },
              userCountry: queryData.context?.userCountry || 'US',
              userDevice: queryData.context?.userDevice || 'desktop',
              sentPrice: {
                baseRate: rate.baseRate,
                taxes: rate.taxes,
                fees: rate.fees,
                totalPrice: rate.totalPrice,
                currency: rate.currency
              },
              landingPageUrl: landingPageUrl || this.buildLandingPageUrl(hotel.hotelId, queryData),
              roomId: rate.roomId,
              packageId: rate.packageId
            });
          }
        }
      }

      logger.debug('Queued price validations', { 
        count: this.validationQueue.length,
        properties: responseData.hotels.map(h => h.hotelId)
      });

    } catch (error) {
      logger.error('Error queueing price validation', { error: error.message });
    }
  }

  /**
   * Build landing page URL for validation
   */
  buildLandingPageUrl(propertyId, queryData) {
    const baseUrl = this.config.business.landingPageBaseUrl || 'https://your-site.com';
    const params = new URLSearchParams({
      hotel_id: propertyId,
      checkin: queryData.checkin,
      nights: queryData.nights,
      adults: queryData.context?.occupancyDetails?.numAdults || 2,
      children: queryData.context?.occupancyDetails?.children?.length || 0
    });

    return `${baseUrl}/hotel/${propertyId}?${params.toString()}`;
  }

  /**
   * Start the validation processor
   */
  startValidationProcessor() {
    setInterval(async () => {
      if (!this.isProcessing && this.validationQueue.length > 0) {
        await this.processValidationQueue();
      }
    }, 30000); // Process every 30 seconds
  }

  /**
   * Process queued validations
   */
  async processValidationQueue() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    const batchSize = 5; // Process 5 validations at a time
    
    try {
      const batch = this.validationQueue.splice(0, batchSize);
      
      if (batch.length > 0) {
        logger.info('Processing price validation batch', { count: batch.length });
        
        const validationPromises = batch.map(validation => 
          this.validateSinglePrice(validation).catch(error => {
            logger.error('Price validation failed', { 
              propertyId: validation.propertyId,
              error: error.message 
            });
            return null;
          })
        );

        const results = await Promise.all(validationPromises);
        const successfulValidations = results.filter(r => r !== null);
        
        // Update accuracy metrics
        this.updateAccuracyMetrics(successfulValidations);
        
        logger.info('Price validation batch completed', {
          processed: batch.length,
          successful: successfulValidations.length,
          remaining: this.validationQueue.length
        });
      }
    } catch (error) {
      logger.error('Error processing validation queue', { error: error.message });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Validate a single price against landing page
   */
  async validateSinglePrice(validation) {
    try {
      // Simulate landing page price extraction
      // In a real implementation, you would scrape or call your booking API
      const landingPagePrice = await this.extractLandingPagePrice(validation);
      
      if (!landingPagePrice) {
        return {
          ...validation,
          status: 'no_price_found',
          accuracy: 0,
          discrepancy: null
        };
      }

      // Calculate accuracy
      const accuracy = this.calculatePriceAccuracy(validation.sentPrice, landingPagePrice);
      const discrepancy = this.calculateDiscrepancy(validation.sentPrice, landingPagePrice);

      const result = {
        ...validation,
        landingPagePrice,
        status: accuracy >= 95 ? 'accurate' : accuracy >= 90 ? 'acceptable' : 'inaccurate',
        accuracy,
        discrepancy,
        validatedAt: new Date().toISOString()
      };

      // Log significant discrepancies
      if (accuracy < 90) {
        logger.warn('Price accuracy issue detected', {
          propertyId: validation.propertyId,
          sentPrice: validation.sentPrice.totalPrice,
          landingPagePrice: landingPagePrice.totalPrice,
          accuracy: accuracy.toFixed(2),
          discrepancy: discrepancy.toFixed(2)
        });
      }

      return result;

    } catch (error) {
      logger.error('Price validation error', {
        propertyId: validation.propertyId,
        error: error.message
      });
      
      return {
        ...validation,
        status: 'validation_error',
        accuracy: 0,
        error: error.message,
        validatedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Extract price from landing page (mock implementation)
   * Replace this with actual landing page scraping or API call
   */
  async extractLandingPagePrice(validation) {
    try {
      // Mock implementation - replace with actual price extraction
      // This could be:
      // 1. Web scraping your landing page
      // 2. Calling your internal booking API
      // 3. Querying your price database directly
      
      const mockPrice = {
        baseRate: validation.sentPrice.baseRate * (0.95 + Math.random() * 0.1), // ±5% variation
        taxes: validation.sentPrice.taxes * (0.95 + Math.random() * 0.1),
        fees: validation.sentPrice.fees * (0.95 + Math.random() * 0.1),
        currency: validation.sentPrice.currency
      };
      
      mockPrice.totalPrice = mockPrice.baseRate + mockPrice.taxes + mockPrice.fees;
      
      // Simulate occasional price mismatches
      if (Math.random() < 0.1) { // 10% chance of significant mismatch
        mockPrice.totalPrice *= 1.2; // 20% higher
      }

      return mockPrice;

      // Real implementation example:
      /*
      const response = await axios.get(validation.landingPageUrl, {
        timeout: 10000,
        headers: {
          'User-Agent': 'PriceValidationBot/1.0'
        }
      });

      // Extract price from HTML or call your booking API
      const extractedPrice = this.parseHtmlForPrice(response.data);
      return extractedPrice;
      */

    } catch (error) {
      logger.error('Failed to extract landing page price', {
        url: validation.landingPageUrl,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Calculate price accuracy percentage
   */
  calculatePriceAccuracy(sentPrice, landingPagePrice) {
    if (!landingPagePrice || landingPagePrice.totalPrice === 0) {
      return 0;
    }

    const difference = Math.abs(sentPrice.totalPrice - landingPagePrice.totalPrice);
    const accuracy = Math.max(0, 100 - (difference / landingPagePrice.totalPrice * 100));
    
    return accuracy;
  }

  /**
   * Calculate price discrepancy amount
   */
  calculateDiscrepancy(sentPrice, landingPagePrice) {
    if (!landingPagePrice) return 0;
    return sentPrice.totalPrice - landingPagePrice.totalPrice;
  }

  /**
   * Update accuracy metrics
   */
  updateAccuracyMetrics(validationResults) {
    const today = new Date().toISOString().split('T')[0];
    
    for (const result of validationResults) {
      if (!this.accuracyData.has(result.propertyId)) {
        this.accuracyData.set(result.propertyId, {
          propertyId: result.propertyId,
          totalValidations: 0,
          accurateCount: 0,
          acceptableCount: 0,
          inaccurateCount: 0,
          averageAccuracy: 0,
          dailyStats: new Map(),
          lastValidation: null,
          issues: []
        });
      }

      const propertyData = this.accuracyData.get(result.propertyId);
      propertyData.totalValidations++;
      propertyData.lastValidation = result.validatedAt;

      // Update counts
      if (result.status === 'accurate') {
        propertyData.accurateCount++;
      } else if (result.status === 'acceptable') {
        propertyData.acceptableCount++;
      } else if (result.status === 'inaccurate') {
        propertyData.inaccurateCount++;
        propertyData.issues.push({
          timestamp: result.validatedAt,
          discrepancy: result.discrepancy,
          accuracy: result.accuracy
        });
      }

      // Update daily stats
      if (!propertyData.dailyStats.has(today)) {
        propertyData.dailyStats.set(today, {
          validations: 0,
          accurate: 0,
          acceptable: 0,
          inaccurate: 0,
          averageAccuracy: 0
        });
      }

      const dailyStats = propertyData.dailyStats.get(today);
      dailyStats.validations++;
      
      if (result.status === 'accurate') dailyStats.accurate++;
      else if (result.status === 'acceptable') dailyStats.acceptable++;
      else if (result.status === 'inaccurate') dailyStats.inaccurate++;

      // Calculate average accuracy
      const totalAccurate = propertyData.accurateCount + propertyData.acceptableCount;
      propertyData.averageAccuracy = (totalAccurate / propertyData.totalValidations) * 100;
      
      dailyStats.averageAccuracy = ((dailyStats.accurate + dailyStats.acceptable) / dailyStats.validations) * 100;
    }
  }

  /**
   * Generate accuracy report
   */
  generateAccuracyReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalProperties: this.accuracyData.size,
        propertiesWithExcellentAccuracy: 0,
        propertiesWithGoodAccuracy: 0,
        propertiesWithPoorAccuracy: 0,
        overallAccuracy: 0
      },
      propertyDetails: [],
      recommendations: []
    };

    let totalValidations = 0;
    let totalAccurate = 0;

    for (const [propertyId, data] of this.accuracyData.entries()) {
      totalValidations += data.totalValidations;
      totalAccurate += data.accurateCount + data.acceptableCount;

      const propertyReport = {
        propertyId,
        averageAccuracy: data.averageAccuracy.toFixed(2),
        totalValidations: data.totalValidations,
        accurateCount: data.accurateCount,
        acceptableCount: data.acceptableCount,
        inaccurateCount: data.inaccurateCount,
        lastValidation: data.lastValidation,
        recentIssues: data.issues.slice(-3) // Last 3 issues
      };

      report.propertyDetails.push(propertyReport);

      // Categorize properties
      if (data.averageAccuracy >= 95) {
        report.summary.propertiesWithExcellentAccuracy++;
      } else if (data.averageAccuracy >= 85) {
        report.summary.propertiesWithGoodAccuracy++;
      } else {
        report.summary.propertiesWithPoorAccuracy++;
      }
    }

    // Calculate overall accuracy
    if (totalValidations > 0) {
      report.summary.overallAccuracy = ((totalAccurate / totalValidations) * 100).toFixed(2);
    }

    // Generate recommendations
    if (report.summary.propertiesWithPoorAccuracy > 0) {
      report.recommendations.push({
        priority: 'HIGH',
        action: `${report.summary.propertiesWithPoorAccuracy} properties have poor price accuracy`,
        details: 'Review price calculation logic and ensure frequent updates'
      });
    }

    if (parseFloat(report.summary.overallAccuracy) < 90) {
      report.recommendations.push({
        priority: 'CRITICAL',
        action: 'Overall price accuracy is below Google\'s requirements',
        details: 'Need to achieve >95% accuracy for 3 consecutive business days'
      });
    }

    return report;
  }

  /**
   * Get daily accuracy trend
   */
  getDailyAccuracyTrend(days = 7) {
    const trend = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      let dayValidations = 0;
      let dayAccurate = 0;
      
      for (const [propertyId, data] of this.accuracyData.entries()) {
        const dailyStats = data.dailyStats.get(dateStr);
        if (dailyStats) {
          dayValidations += dailyStats.validations;
          dayAccurate += dailyStats.accurate + dailyStats.acceptable;
        }
      }
      
      const accuracy = dayValidations > 0 ? (dayAccurate / dayValidations * 100).toFixed(2) : 0;
      
      trend.push({
        date: dateStr,
        validations: dayValidations,
        accuracy: parseFloat(accuracy)
      });
    }
    
    return trend;
  }
}

module.exports = PriceAccuracyMonitor;
